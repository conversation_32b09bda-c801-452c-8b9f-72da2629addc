#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地理题库系统启动脚本
支持开发环境和生产环境
"""

import os
import sys
import argparse

# 确保项目根目录在Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 切换到项目根目录
os.chdir(project_root)

def main():
    """启动应用"""
    parser = argparse.ArgumentParser(description='地理题库系统启动脚本')
    parser.add_argument('--env', choices=['development', 'production'], 
                       default='production', help='运行环境 (默认: production)')
    parser.add_argument('--host', default='0.0.0.0', help='绑定主机 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5002, help='端口号 (默认: 5002)')
    parser.add_argument('--debug', action='store_true', help='开启调试模式')
    
    args = parser.parse_args()
    
    # 设置环境变量
    os.environ['FLASK_ENV'] = args.env
    
    print("🚀 地理题库系统启动...")
    print("=" * 50)
    print(f"🔧 运行环境: {args.env}")
    print(f"🔧 绑定地址: {args.host}:{args.port}")
    print(f"🔧 调试模式: {'开启' if args.debug else '关闭'}")
    
    # 检查数据库密码（生产环境必须）
    if args.env == 'production' and not os.environ.get('DB_PASSWORD'):
        print("❌ 错误: 生产环境未设置数据库密码")
        print("请设置环境变量: export DB_PASSWORD=your_password")
        print("或在宝塔面板中设置环境变量 DB_PASSWORD")
        sys.exit(1)
    
    # 导入应用
    try:
        # 调试信息：显示当前工作目录和Python路径
        print(f"🔍 当前工作目录: {os.getcwd()}")
        print(f"🔍 Python路径: {sys.path[0]}")
        print(f"🔍 检查src目录: {os.path.exists('src')}")

        from src.app import app, socketio, current_env, config_class
        
        print(f"✅ 当前环境: {current_env}")
        print(f"✅ 配置类: {config_class.__name__}")
        print(f"✅ 数据库主机: {config_class.DB_CONFIG['host']}")
        print("=" * 50)
        print(f"📍 访问地址: http://{args.host}:{args.port}")
        print("=" * 50)
        
        # 启动应用
        socketio.run(
            app,
            debug=args.debug or (args.env == 'development'),
            host=args.host,
            port=args.port,
            allow_unsafe_werkzeug=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
