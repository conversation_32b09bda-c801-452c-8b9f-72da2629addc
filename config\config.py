import os
import json
import pymysql
from typing import Dict, Any

class Config:
    """应用配置类 - 基础配置"""

    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'geo-question-web-secret-key-2024'

    # 基础数据库配置 - 生产环境默认配置
    DB_CONFIG = {
        'host': os.environ.get('DB_HOST', 'localhost'),  # 生产环境默认使用localhost
        'port': int(os.environ.get('DB_PORT', 3306)),
        'user': os.environ.get('DB_USER', 'geo_questions'),
        'password': os.environ.get('DB_PASSWORD', ''),  # 必须通过环境变量设置
        'database': os.environ.get('DB_NAME', 'geo_questions'),
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor,
        'connect_timeout': 30,
        'read_timeout': 60,
        'autocommit': True,  # 自动提交
        'max_allowed_packet': 16777216  # 16MB，支持大图片
    }

    # 是否使用SQLite（开发环境可选）
    USE_SQLITE = os.environ.get('USE_SQLITE', '0') == '1'
    SQLITE_DB_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'dev_database.db')
    
    # Redis配置（用于Celery）
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # Celery配置
    CELERY_BROKER_URL = REDIS_URL
    CELERY_RESULT_BACKEND = REDIS_URL
    
    # 文件路径配置
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 指向geo_question_web目录 (上一级目录)
    LOG_FOLDER = os.path.join(BASE_DIR, 'logs')

    # 预处理程序配置 - 使用geo_question_web内部目录
    PREPROCESSOR_CONFIG = {
        'work_dir': os.path.join(BASE_DIR, 'data', 'preprocessor_data'),  # 工作目录
        'output_dir': os.path.join(BASE_DIR, 'data', 'preprocessor_data', 'output'),
        'cache_dir': os.path.join(BASE_DIR, 'data', 'preprocessor_data', 'cache'),
        'images_dir': os.path.join(BASE_DIR, 'data', 'preprocessor_data', 'images'),
        'tables_dir': os.path.join(BASE_DIR, 'data', 'preprocessor_data', 'tables'),
        'config_file': os.path.join(BASE_DIR, 'data', 'preprocessor_data', 'config.json')
    }
    
    # 推送配置 - 支持环境变量
    PUSHER_CONFIG = {
        'pushplus_token': os.environ.get('PUSHPLUS_TOKEN', 'c9d4a18590d64b6abe1cff330e0f7922'),
        # HTTPS图片URL（支持域名）
        # 生产环境建议设置为你的域名，如: https://yourdomain.com/images/
        'image_url_base': os.environ.get('IMAGE_URL_BASE', 'https://gzdlgeeker.top/images/'),
        # 推送模式：'full' 完整HTML, 'simple' 简化版本
        'push_mode': os.environ.get('PUSH_MODE', 'simple'),
        'template_file': os.path.join(BASE_DIR, 'src', 'templates', 'push_template.html')
    }
    
    # 确保必要目录存在
    @staticmethod
    def init_app():
        """初始化应用目录"""
        dirs_to_create = [
            Config.LOG_FOLDER,
            # 只在需要时创建预处理工作目录，不预先创建
        ]

        for directory in dirs_to_create:
            os.makedirs(directory, exist_ok=True)

class ProductionConfig(Config):
    """生产环境配置 - 使用localhost连接MySQL数据库"""

    # 生产环境关闭调试模式
    DEBUG = False

    # 生产环境使用更安全的密钥
    SECRET_KEY = os.environ.get('SECRET_KEY') or os.urandom(32).hex()

    # 生产环境日志配置
    LOG_LEVEL = 'INFO'

    # 生产环境强制使用MySQL，不使用SQLite
    USE_SQLITE = False

    # 生产环境数据库配置 - 使用localhost
    DB_CONFIG = {
        'host': os.environ.get('DB_HOST', 'localhost'),  # 生产环境使用localhost
        'port': int(os.environ.get('DB_PORT', 3306)),
        'user': os.environ.get('DB_USER', 'geo_questions'),
        'password': os.environ.get('DB_PASSWORD', ''),  # 必须通过环境变量设置
        'database': os.environ.get('DB_NAME', 'geo_questions'),
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor,
        'connect_timeout': 30,
        'read_timeout': 60,
        'autocommit': True,
        'max_allowed_packet': 16777216
    }

    @staticmethod
    def init_app():
        """生产环境初始化"""
        Config.init_app()

        # 生产环境可以添加额外的初始化逻辑
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

class DevelopmentConfig(Config):
    """开发环境配置 - 使用远程IP连接MySQL数据库"""

    DEBUG = True  # 开发环境开启调试模式
    LOG_LEVEL = 'DEBUG'

    # 开发环境可选择使用SQLite（设置环境变量USE_SQLITE=1启用）
    USE_SQLITE = os.environ.get('USE_SQLITE', '0') == '1'
    SQLITE_DB_PATH = os.path.join(Config.BASE_DIR, 'dev_database.db')

    # 开发环境MySQL配置 - 使用远程IP *************
    DB_CONFIG = {
        'host': os.environ.get('DB_HOST', '*************'),  # 开发环境使用远程IP
        'port': int(os.environ.get('DB_PORT', 3306)),
        'user': os.environ.get('DB_USER', 'geo_questions'),
        'password': os.environ.get('DB_PASSWORD', ''),  # 通过环境变量设置
        'database': os.environ.get('DB_NAME', 'geo_questions'),
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor,
        'connect_timeout': 30,
        'read_timeout': 60,
        'autocommit': True,
        'max_allowed_packet': 16777216
    }

    @staticmethod
    def init_app():
        """开发环境初始化"""
        Config.init_app()

        # 开发环境日志配置
        import logging
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def load_preprocessor_config() -> Dict[str, Any]:
    """加载预处理程序配置"""
    config_path = Config.PREPROCESSOR_CONFIG['config_file']
    
    default_config = {
        "api": {
            "deepseek_api_base": "https://api.deepseek.com/v1",
            "deepseek_api_key": "your_api_key_here",
            "moonshot_api_key": "your_api_key_here"
        },
        "files": [],
        "use_cache": True,
        "verify_ssl": False,
        "manual_analysis": False
    }
    
    if not os.path.exists(config_path):
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=4)
        return default_config
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return default_config

def save_preprocessor_config(config: Dict[str, Any]):
    """保存预处理程序配置"""
    config_path = Config.PREPROCESSOR_CONFIG['config_file']
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)
