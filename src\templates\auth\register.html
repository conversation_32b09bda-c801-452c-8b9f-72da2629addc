<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 地理题库系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        .register-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .input-group-text {
            background: transparent;
            border-right: none;
        }
        .form-control {
            border-left: none;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .validation-feedback {
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        .is-valid {
            border-color: #198754;
        }
        .is-invalid {
            border-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="register-card">
                    <div class="register-header">
                        <h3 class="mb-0">
                            <i class="bi bi-geo-alt-fill me-2"></i>
                            地理题库系统
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">用户注册</p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- 显示Flash消息 -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST" action="{{ url_for('auth.register') }}" id="registerForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-person"></i>
                                    </span>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           placeholder="3-20位字母、数字、下划线" required>
                                </div>
                                <div class="validation-feedback" id="username-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="请输入邮箱地址" required>
                                </div>
                                <div class="validation-feedback" id="email-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">密码</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="至少6位字符" required>
                                </div>
                                <div class="validation-feedback" id="password-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">确认密码</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock-fill"></i>
                                    </span>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           placeholder="请再次输入密码" required>
                                </div>
                                <div class="validation-feedback" id="confirm-password-feedback"></div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-register">
                                    <i class="bi bi-person-plus me-2"></i>
                                    注册
                                </button>
                            </div>
                        </form>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-0">已有账号？</p>
                            <a href="{{ url_for('auth.login') }}" class="text-decoration-none">
                                <i class="bi bi-box-arrow-in-right me-1"></i>
                                立即登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registerForm');
            const username = document.getElementById('username');
            const email = document.getElementById('email');
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirm_password');
            
            // 用户名验证
            username.addEventListener('blur', function() {
                const value = this.value.trim();
                const feedback = document.getElementById('username-feedback');
                
                if (value.length < 3 || value.length > 20) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                    feedback.textContent = '用户名长度应为3-20位';
                    feedback.className = 'validation-feedback text-danger';
                } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                    feedback.textContent = '用户名只能包含字母、数字、下划线';
                    feedback.className = 'validation-feedback text-danger';
                } else {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                    feedback.textContent = '用户名格式正确';
                    feedback.className = 'validation-feedback text-success';
                }
            });
            
            // 邮箱验证
            email.addEventListener('blur', function() {
                const value = this.value.trim();
                const feedback = document.getElementById('email-feedback');
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                
                if (!emailRegex.test(value)) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                    feedback.textContent = '请输入有效的邮箱地址';
                    feedback.className = 'validation-feedback text-danger';
                } else {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                    feedback.textContent = '邮箱格式正确';
                    feedback.className = 'validation-feedback text-success';
                }
            });
            
            // 密码验证
            password.addEventListener('blur', function() {
                const value = this.value;
                const feedback = document.getElementById('password-feedback');
                
                if (value.length < 6) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                    feedback.textContent = '密码长度至少6位';
                    feedback.className = 'validation-feedback text-danger';
                } else {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                    feedback.textContent = '密码强度符合要求';
                    feedback.className = 'validation-feedback text-success';
                }
                
                // 重新验证确认密码
                if (confirmPassword.value) {
                    confirmPassword.dispatchEvent(new Event('blur'));
                }
            });
            
            // 确认密码验证
            confirmPassword.addEventListener('blur', function() {
                const value = this.value;
                const feedback = document.getElementById('confirm-password-feedback');
                
                if (value !== password.value) {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                    feedback.textContent = '两次输入的密码不一致';
                    feedback.className = 'validation-feedback text-danger';
                } else if (value.length > 0) {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                    feedback.textContent = '密码确认正确';
                    feedback.className = 'validation-feedback text-success';
                }
            });
        });
    </script>
</body>
</html>
