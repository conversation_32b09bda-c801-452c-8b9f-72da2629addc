# 地理题库系统环境配置文件示例
# 复制此文件为 .env 并根据你的环境修改配置

# ==================== 环境设置 ====================
# 环境类型: development(开发环境) 或 production(生产环境)
FLASK_ENV=development

# ==================== 数据库配置 ====================
# 是否使用SQLite数据库 (1=启用, 0=禁用，使用MySQL)
# 注意：生产环境强制使用MySQL，此选项仅在开发环境有效
USE_SQLITE=0

# MySQL数据库配置
DB_HOST=*************
DB_PORT=3306
DB_USER=geo_questions
DB_PASSWORD=your_password_here
DB_NAME=geo_questions

# ==================== Flask配置 ====================
SECRET_KEY=your-secret-key-here
HOST=0.0.0.0
PORT=5002

# ==================== 推送配置 ====================
PUSHPLUS_TOKEN=c9d4a18590d64b6abe1cff330e0f7922
IMAGE_URL_BASE=https://gzdlgeeker.top/images/
PUSH_MODE=simple

# ==================== Redis配置 ====================
REDIS_URL=redis://localhost:6379/0
