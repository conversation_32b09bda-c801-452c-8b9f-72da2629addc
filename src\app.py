from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_file
from flask_socketio import Socket<PERSON>, emit
from flask_login import LoginManager, login_required, current_user
import os
import sys
import logging
from datetime import datetime
import pymysql
import sqlite3
import json
import asyncio
import zipfile
import tempfile
from pyvis.network import Network

# 确保项目根目录在Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.config import Config, config, load_preprocessor_config, save_preprocessor_config
from .modules.preprocessor import PreprocessorManager
from .modules.uploader import UploaderManager
from .modules.pusher import PusherManager
from .modules.database import DatabaseManager
from .modules.models import User
from .modules.auth import auth_bp, init_auth
from .modules.quiz import quiz_bp, init_quiz
from .modules.decorators import admin_required
from .modules.neo4j_manager import neo4j_manager

# 加载环境配置
def load_env_config():
    """加载环境配置文件"""
    # 获取环境类型
    env = os.environ.get('FLASK_ENV', 'development')

    # 尝试加载对应的环境配置文件
    env_file = os.path.join(project_root, 'config', 'environments', f'.env.{env}')
    if os.path.exists(env_file):
        print(f"📄 加载环境配置文件: {env_file}")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
    else:
        print(f"⚠️  环境配置文件 {env_file} 不存在，使用默认配置")

    return env

# 加载环境配置
current_env = load_env_config()


# 创建Flask应用
app = Flask(__name__)

# 根据环境加载对应的配置类
config_class = config.get(current_env, config['default'])
app.config.from_object(config_class)

# 初始化Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = '请先登录'
login_manager.login_message_category = 'info'

print(f"🔧 当前环境: {current_env}")
print(f"🔧 配置类: {config_class.__name__}")
print(f"🔧 数据库主机: {config_class.DB_CONFIG['host']}")
print(f"🔧 使用SQLite: {getattr(config_class, 'USE_SQLITE', False)}")

# 初始化SocketIO
socketio = SocketIO(
    app,
    cors_allowed_origins="*",
    async_mode='threading',  # 使用threading模式而不是eventlet
    logger=True,
    engineio_logger=True
)

# 配置日志
handlers = [logging.StreamHandler()]  # 至少保证控制台输出

# 尝试添加文件日志处理器
try:
    file_handler = logging.FileHandler(os.path.join(Config.LOG_FOLDER, 'app.log'))
    handlers.append(file_handler)
except (PermissionError, FileNotFoundError) as e:
    print(f"警告：无法创建日志文件，将只使用控制台输出: {e}")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=handlers
)
logger = logging.getLogger(__name__)

# 初始化应用目录
config_class.init_app()

# 初始化数据库管理器
db_manager = DatabaseManager(config_class.DB_CONFIG)

# 初始化认证模块
init_auth(db_manager)

# 初始化刷题模块
init_quiz(db_manager)

# 注册蓝图
app.register_blueprint(auth_bp)
app.register_blueprint(quiz_bp)

# Flask-Login用户加载函数
@login_manager.user_loader
def load_user(user_id):
    """加载用户"""
    return User.get_by_id(int(user_id), db_manager)

# 初始化预处理程序管理器
preprocessor_manager = PreprocessorManager(socketio)

# 初始化数据库上传管理器
uploader_manager = UploaderManager(socketio)

# 初始化推送管理器
pusher_manager = PusherManager(socketio)

def get_db_connection():
    """获取数据库连接"""
    try:
        # 检查是否使用SQLite
        if os.environ.get('USE_SQLITE') == '1' or getattr(config_class, 'USE_SQLITE', False):
            # 使用SQLite
            db_path = getattr(config_class, 'SQLITE_DB_PATH', os.path.join(config_class.BASE_DIR, 'dev_database.db'))
            connection = sqlite3.connect(db_path)
            connection.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
            logger.info(f"使用SQLite数据库: {db_path}")
            return connection
        else:
            # 使用MySQL
            connection = pymysql.connect(**config_class.DB_CONFIG)
            logger.info(f"使用MySQL数据库: {config_class.DB_CONFIG['host']}:{config_class.DB_CONFIG['port']}")
            return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def get_database_stats():
    """获取数据库统计信息"""
    connection = get_db_connection()
    if not connection:
        return None

    try:
        cursor = connection.cursor()

        # 获取试卷数量
        cursor.execute("SELECT COUNT(*) as count FROM papers")
        papers_count = cursor.fetchone()['count']

        # 获取题目数量
        cursor.execute("SELECT COUNT(*) as count FROM questions")
        questions_count = cursor.fetchone()['count']

        # 获取最近的试卷
        cursor.execute("""
            SELECT paper_name, created_at
            FROM papers
            ORDER BY created_at DESC
            LIMIT 5
        """)
        recent_papers = cursor.fetchall()

        return {
            'papers_count': papers_count,
            'questions_count': questions_count,
            'recent_papers': recent_papers
        }
    except Exception as e:
        logger.error(f"获取数据库统计信息失败: {e}")
        return None
    finally:
        connection.close()

@app.route('/')
def home():
    """网站首页"""
    return render_template('home.html')

@app.route('/admin')
@admin_required
def admin_index():
    """管理后台主页"""
    stats = get_database_stats()
    return render_template('admin/dashboard.html', stats=stats)

@app.route('/admin/preprocessor')
@admin_required
def admin_preprocessor():
    """预处理程序页面"""
    config = load_preprocessor_config()
    return render_template('admin/preprocessor.html', config=config)

@app.route('/admin/uploader')
@admin_required
def admin_uploader():
    """数据库上传页面"""
    stats = get_database_stats()
    return render_template('admin/uploader.html', stats=stats)

@app.route('/admin/pusher')
@admin_required
def admin_pusher():
    """推送管理页面"""
    return render_template('admin/pusher.html')

@app.route('/admin/config')
@admin_required
def admin_config_page():
    """配置管理页面"""
    preprocessor_config = load_preprocessor_config()
    return render_template('admin/config.html',
                         preprocessor_config=preprocessor_config,
                         pusher_config=Config.PUSHER_CONFIG)

def generate_pyvis_graph(node_type_filter='', relationship_type_filter='', limit=100):
    """使用PyVis生成力导向知识图谱"""
    try:
        # 从Neo4j获取真实数据
        app.logger.info(f"从Neo4j获取图谱数据，筛选条件: 节点类型={node_type_filter}, 关系类型={relationship_type_filter}, 限制={limit}")

        # 从Neo4j获取真实数据
        try:
            data = neo4j_manager.get_graph_data(
                limit=limit,
                node_type_filter=node_type_filter,
                relationship_type_filter=relationship_type_filter
            )
            app.logger.info(f"成功获取Neo4j数据: {len(data['nodes'])}个节点, {len(data['edges'])}条边")
        except Exception as e:
            app.logger.error(f"Neo4j数据获取失败: {str(e)}")
            # 如果Neo4j失败，返回错误信息而不是假数据
            raise Exception(f"无法连接到Neo4j数据库: {str(e)}")

        # 创建PyVis网络
        net = Network(
            height="600px",
            width="100%",
            bgcolor="#f5f7fa",
            font_color="#2D3436",
            directed=True
        )

        # 配置物理引擎和交互功能（移除JavaScript函数避免JSON解析错误）
        net.set_options("""
        var options = {
          "physics": {
            "enabled": true,
            "forceAtlas2Based": {
              "gravitationalConstant": -50,
              "centralGravity": 0.01,
              "springLength": 100,
              "springConstant": 0.08,
              "damping": 0.4,
              "avoidOverlap": 1
            },
            "maxVelocity": 50,
            "minVelocity": 0.1,
            "solver": "forceAtlas2Based",
            "stabilization": {"iterations": 100}
          },
          "nodes": {
            "borderWidth": 2,
            "borderWidthSelected": 4,
            "font": {
              "size": 12,
              "face": "Microsoft YaHei"
            }
          },
          "edges": {
            "arrows": {
              "to": {"enabled": true, "scaleFactor": 0.8}
            },
            "font": {
              "size": 10,
              "face": "Microsoft YaHei"
            },
            "smooth": {
              "enabled": true,
              "type": "dynamic"
            }
          },
          "interaction": {
            "hover": true,
            "selectConnectedEdges": true,
            "hoverConnectedEdges": true
          }
        }
        """)

        # 节点颜色映射
        node_colors = {
            '地理概念': '#6C5CE7',
            '知识点': '#00B894',
            '题目': '#0984E3',
            '试卷': '#E17055',
            '章节': '#FDCB6E',
            '单元': '#E84393',
            'default': '#74B9FF'
        }

        # 边颜色映射
        edge_colors = {
            '包含': '#0984E3',
            '关联': '#E17055',
            '依赖': '#FDCB6E',
            '属于': '#00B894',
            '相关': '#6C5CE7',
            'default': '#B2BEC3'
        }

        # 添加节点
        for node in data['nodes']:
            node_id = str(node['id'])
            # 处理标签：优先使用label字段，如果没有则使用labels数组的第一个
            label = node.get('label', 'default')
            if not label or label == 'default':
                labels = node.get('labels', [])
                label = labels[0] if labels else 'default'

            # 获取节点名称：优先从properties中获取name，然后是title，最后使用默认名称
            properties = node.get('properties', {})
            name = properties.get('name') or properties.get('title') or properties.get('content') or f'节点{node_id}'

            # 应用节点类型筛选
            if node_type_filter and label != node_type_filter:
                continue

            color = node_colors.get(label, node_colors['default'])

            # 根据节点类型设置大小
            size = 25
            if label == '地理概念':
                size = 35
            elif label == '知识点':
                size = 30
            elif label == '题目':
                size = 20

            net.add_node(
                node_id,
                label=name,
                color=color,
                size=size,
                title=f"类型: {label}<br>名称: {name}",
                group=label
            )

        # 获取已添加的节点ID集合
        added_nodes = set(net.get_nodes())

        # 添加边
        for edge in data['edges']:
            source_id = str(edge['source'])  # 确保ID为字符串
            target_id = str(edge['target'])  # 确保ID为字符串
            edge_type = edge.get('type', 'default')

            # 只添加两端节点都存在的边
            if source_id not in added_nodes or target_id not in added_nodes:
                continue

            # 应用关系类型筛选
            if relationship_type_filter and edge_type != relationship_type_filter:
                continue

            color = edge_colors.get(edge_type, edge_colors['default'])

            # 根据关系类型设置边的宽度
            width = 2
            if edge_type == '包含':
                width = 3
            elif edge_type == '属于':
                width = 2.5

            net.add_edge(
                source_id,
                target_id,
                label=edge_type,
                color=color,
                width=width,
                title=f"关系: {edge_type}"
            )

        # 生成HTML
        html_content = net.generate_html()
        app.logger.info(f"🔍 [DEBUG] PyVis生成的HTML长度: {len(html_content)}")

        # 直接返回PyVis生成的完整HTML，但添加调试信息
        # 在HTML中添加调试脚本
        debug_script = '''
        <script>
            console.log('🔍 [DEBUG] PyVis HTML已加载');

            // 等待一段时间后检查网络对象
            setTimeout(function() {
                console.log('🔍 [DEBUG] 检查PyVis网络对象...');

                // 查找可能的网络对象
                let networkObj = null;
                let nodesObj = null;
                let edgesObj = null;

                // 方法1：检查常见的全局变量名
                const possibleNames = ['network', 'mynetworkid', 'vis_network'];
                for (let name of possibleNames) {
                    if (window[name] && typeof window[name] === 'object') {
                        console.log('🔍 [DEBUG] 找到网络对象:', name, window[name]);
                        networkObj = window[name];
                        break;
                    }
                }

                // 方法2：检查所有全局变量
                if (!networkObj) {
                    for (let key in window) {
                        if (window[key] && typeof window[key] === 'object' &&
                            window[key].body && window[key].body.nodes) {
                            console.log('🔍 [DEBUG] 通过遍历找到网络对象:', key, window[key]);
                            networkObj = window[key];
                            break;
                        }
                    }
                }

                // 查找节点和边数据
                const possibleDataNames = ['nodes', 'edges', 'nodeDataset', 'edgeDataset'];
                for (let name of possibleDataNames) {
                    if (window[name] && typeof window[name] === 'object') {
                        if (name.includes('node') || name === 'nodes') {
                            nodesObj = window[name];
                            console.log('🔍 [DEBUG] 找到节点数据:', name, nodesObj);
                        }
                        if (name.includes('edge') || name === 'edges') {
                            edgesObj = window[name];
                            console.log('🔍 [DEBUG] 找到边数据:', name, edgesObj);
                        }
                    }
                }

                // 如果找到网络对象，设置全局变量
                if (networkObj) {
                    window.knowledgeGraphNetwork = networkObj;
                    console.log('🔍 [DEBUG] 设置全局网络对象成功');

                    // 尝试从网络对象获取数据
                    if (networkObj.body && networkObj.body.data) {
                        window.knowledgeGraphNodes = networkObj.body.data.nodes;
                        window.knowledgeGraphEdges = networkObj.body.data.edges;
                        console.log('🔍 [DEBUG] 从网络对象获取数据成功');
                    } else if (nodesObj && edgesObj) {
                        window.knowledgeGraphNodes = nodesObj;
                        window.knowledgeGraphEdges = edgesObj;
                        console.log('🔍 [DEBUG] 使用独立的数据对象');
                    }

                    // 输出最终状态
                    if (window.knowledgeGraphNodes && window.knowledgeGraphEdges) {
                        try {
                            const nodeCount = window.knowledgeGraphNodes.length || window.knowledgeGraphNodes.get().length;
                            const edgeCount = window.knowledgeGraphEdges.length || window.knowledgeGraphEdges.get().length;
                            console.log('🔍 [DEBUG] 最终数据统计 - 节点:', nodeCount, '边:', edgeCount);
                        } catch (e) {
                            console.error('🔍 [DEBUG] 获取数据统计失败:', e);
                        }
                    }
                } else {
                    console.error('🔍 [DEBUG] 未找到网络对象');
                    console.log('🔍 [DEBUG] 当前全局变量:', Object.keys(window).filter(k => !k.startsWith('webkit')));
                }

                // 检查容器状态
                const container = document.getElementById('mynetwork');
                if (container) {
                    console.log('🔍 [DEBUG] 容器状态:', {
                        width: container.offsetWidth,
                        height: container.offsetHeight,
                        children: container.children.length,
                        innerHTML: container.innerHTML.substring(0, 100) + '...'
                    });

                    // 检查canvas元素
                    const canvas = container.querySelector('canvas');
                    if (canvas) {
                        console.log('🔍 [DEBUG] Canvas状态:', {
                            width: canvas.width,
                            height: canvas.height,
                            offsetWidth: canvas.offsetWidth,
                            offsetHeight: canvas.offsetHeight,
                            style: canvas.style.cssText,
                            display: window.getComputedStyle(canvas).display,
                            visibility: window.getComputedStyle(canvas).visibility,
                            opacity: window.getComputedStyle(canvas).opacity
                        });

                        // 尝试获取canvas上下文
                        const ctx = canvas.getContext('2d');
                        if (ctx) {
                            console.log('🔍 [DEBUG] Canvas上下文存在，尝试检查内容...');
                            // 检查canvas是否有内容
                            const imageData = ctx.getImageData(0, 0, Math.min(canvas.width, 100), Math.min(canvas.height, 100));
                            const hasContent = imageData.data.some(pixel => pixel !== 0);
                            console.log('🔍 [DEBUG] Canvas有内容:', hasContent);

                            // 如果canvas没有内容，尝试手动触发渲染
                            if (!hasContent && networkObj) {
                                console.log('🔍 [DEBUG] Canvas为空，尝试手动触发渲染...');
                                try {
                                    // 尝试重新设置数据
                                    if (networkObj.setData && nodesObj && edgesObj) {
                                        networkObj.setData({ nodes: nodesObj, edges: edgesObj });
                                        console.log('🔍 [DEBUG] 重新设置数据完成');
                                    }

                                    // 尝试手动触发重绘
                                    if (networkObj.redraw) {
                                        networkObj.redraw();
                                        console.log('🔍 [DEBUG] 手动重绘完成');
                                    }

                                    // 尝试适应视图
                                    if (networkObj.fit) {
                                        networkObj.fit();
                                        console.log('🔍 [DEBUG] 适应视图完成');
                                    }

                                    // 等待一段时间后再次检查
                                    setTimeout(() => {
                                        const newImageData = ctx.getImageData(0, 0, Math.min(canvas.width, 100), Math.min(canvas.height, 100));
                                        const newHasContent = newImageData.data.some(pixel => pixel !== 0);
                                        console.log('🔍 [DEBUG] 手动渲染后Canvas有内容:', newHasContent);

                                        if (!newHasContent) {
                                            console.error('🔍 [DEBUG] 手动渲染失败，检查网络状态...');
                                            console.log('🔍 [DEBUG] 网络body状态:', networkObj.body ? 'exists' : 'missing');
                                            if (networkObj.body) {
                                                console.log('🔍 [DEBUG] 网络节点数量:', networkObj.body.data.nodes ? networkObj.body.data.nodes.length : 'unknown');
                                                console.log('🔍 [DEBUG] 网络边数量:', networkObj.body.data.edges ? networkObj.body.data.edges.length : 'unknown');
                                            }
                                        }
                                    }, 1000);
                                } catch (e) {
                                    console.error('🔍 [DEBUG] 手动渲染出错:', e);
                                }
                            }
                        } else {
                            console.error('🔍 [DEBUG] 无法获取Canvas上下文');
                        }
                    } else {
                        console.error('🔍 [DEBUG] 未找到Canvas元素');
                        console.log('🔍 [DEBUG] 容器子元素:', Array.from(container.children).map(child => ({
                            tagName: child.tagName,
                            className: child.className,
                            id: child.id
                        })));
                    }
                } else {
                    console.error('🔍 [DEBUG] 未找到mynetwork容器');
                }
            }, 1000);
        </script>
        '''

        # 在HTML末尾添加调试脚本
        html_with_debug = html_content.replace('</body>', debug_script + '</body>')
        app.logger.info(f"🔍 [DEBUG] 添加调试脚本后HTML长度: {len(html_with_debug)}")

        return html_with_debug

    except Exception as e:
        app.logger.error(f"生成PyVis图谱失败: {str(e)}")
        return f'<div class="alert alert-danger">生成图谱失败: {str(e)}</div>'

@app.route('/admin/knowledge-graph')
@admin_required
def admin_knowledge_graph():
    """知识图谱页面 - 使用PyVis力导向布局"""
    # 获取筛选参数
    node_type = request.args.get('node_type', '')
    relationship_type = request.args.get('relationship_type', '')
    limit = request.args.get('limit', 100, type=int)

    # 首先检查Neo4j连接
    if not neo4j_manager.test_connection():
        app.logger.error("Neo4j数据库连接不可用")
        return render_template('admin/knowledge_graph.html',
                             error="Neo4j数据库连接不可用，请检查数据库服务是否正常运行",
                             node_types=[],
                             relationship_types=[],
                             current_node_type=node_type,
                             current_relationship_type=relationship_type,
                             current_limit=limit)

    try:
        # 生成PyVis知识图谱
        graph_html = generate_pyvis_graph(node_type, relationship_type, limit)

        # 获取类型信息用于筛选器
        node_types = neo4j_manager.get_node_types()
        relationship_types = neo4j_manager.get_relationship_types()

        return render_template('admin/knowledge_graph.html',
                             graph_html=graph_html,
                             node_types=node_types,
                             relationship_types=relationship_types,
                             current_node_type=node_type,
                             current_relationship_type=relationship_type,
                             current_limit=limit)
    except Exception as e:
        app.logger.error(f"生成知识图谱失败: {str(e)}")
        return render_template('admin/knowledge_graph.html',
                             error=f"生成知识图谱失败: {str(e)}",
                             node_types=[],
                             relationship_types=[],
                             current_node_type=node_type,
                             current_relationship_type=relationship_type,
                             current_limit=limit)

@app.route('/api/knowledge-graph/connection-status')
@admin_required
def api_connection_status():
    """检查Neo4j连接状态API"""
    try:
        app.logger.info("开始检查Neo4j连接状态")
        is_connected = neo4j_manager.test_connection()
        app.logger.info(f"Neo4j连接状态: {is_connected}")

        if is_connected:
            # 获取数据统计
            try:
                data = neo4j_manager.get_graph_data(limit=1000)
                return jsonify({
                    'success': True,
                    'connected': True,
                    'data_source': 'Neo4j数据库',
                    'node_count': len(data['nodes']),
                    'edge_count': len(data['edges'])
                })
            except Exception as e:
                return jsonify({
                    'success': True,
                    'connected': True,
                    'data_source': 'Neo4j数据库 (数据获取失败)',
                    'node_count': 0,
                    'edge_count': 0,
                    'error': str(e)
                })
        else:
            return jsonify({
                'success': True,
                'connected': False,
                'data_source': 'Neo4j连接失败',
                'node_count': 0,
                'edge_count': 0,
                'message': '请检查Neo4j服务是否正常运行'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'connected': False,
            'data_source': '连接检查失败',
            'error': str(e)
        })

@app.route('/api/knowledge-graph/data')
@admin_required
def api_graph_data():
    """获取图谱数据API - 支持筛选"""
    try:
        node_type = request.args.get('node_type', '')
        relationship_type = request.args.get('relationship_type', '')
        limit = request.args.get('limit', 100, type=int)

        app.logger.info(f"API获取图谱数据: node_type={node_type}, relationship_type={relationship_type}, limit={limit}")

        # 生成PyVis图谱HTML
        graph_html = generate_pyvis_graph(node_type, relationship_type, limit)

        # 获取筛选后的数据用于统计
        filtered_data = neo4j_manager.get_graph_data(
            limit=limit,
            node_type_filter=node_type,
            relationship_type_filter=relationship_type
        )

        # 获取总数据用于对比
        total_data = neo4j_manager.get_graph_data(limit=1000)  # 获取更多数据用于总数统计

        return jsonify({
            'success': True,
            'graph_html': graph_html,
            'stats': {
                'node_count': len(filtered_data['nodes']),
                'edge_count': len(filtered_data['edges']),
                'total_nodes': len(total_data['nodes']),
                'total_edges': len(total_data['edges'])
            }
        })

    except Exception as e:
        app.logger.error(f"获取图谱数据API失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/knowledge-graph/node-details/<node_id>')
@admin_required
def api_node_details(node_id):
    """获取节点详细信息API"""
    try:
        # 尝试从Neo4j获取详细信息
        try:
            details = neo4j_manager.get_node_details(int(node_id))
            if details:
                return jsonify({'success': True, 'node': details})
        except Exception:
            pass

        # 如果Neo4j失败，返回基本信息
        return jsonify({
            'success': True,
            'node': {
                'id': node_id,
                'name': f'节点{node_id}',
                'type': '未知类型',
                'properties': {},
                'connections': []
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取节点详情失败: {str(e)}'})

@app.route('/api/config/preprocessor', methods=['GET', 'POST'])
@admin_required
def api_preprocessor_config():
    """预处理程序配置API"""
    if request.method == 'GET':
        config = load_preprocessor_config()
        return jsonify(config)

    elif request.method == 'POST':
        try:
            config = request.get_json()
            save_preprocessor_config(config)
            return jsonify({'success': True, 'message': '配置保存成功'})
        except Exception as e:
            return jsonify({'success': False, 'message': f'配置保存失败: {str(e)}'})

@app.route('/api/database/stats')
@admin_required
def api_database_stats():
    """数据库统计信息API"""
    stats = get_database_stats()
    if stats:
        return jsonify(stats)
    else:
        return jsonify({'error': '无法获取数据库统计信息'}), 500

@app.route('/api/test-connection')
@admin_required
def api_test_connection():
    """测试数据库连接API"""
    connection = get_db_connection()
    if connection:
        connection.close()
        return jsonify({'success': True, 'message': '数据库连接成功'})
    else:
        return jsonify({'success': False, 'message': '数据库连接失败'})


@app.route('/api/preprocessor/start', methods=['POST'])
@admin_required
def api_preprocessor_start():
    """启动预处理任务API"""
    try:
        # 在新的事件循环中运行异步任务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(preprocessor_manager.process_documents())
        loop.close()

        if result == True:
            return jsonify({'success': True, 'message': '处理任务已启动'})
        elif result == 'cache_choice_required':
            return jsonify({'success': True, 'message': '发现缓存文件，等待用户选择', 'status': 'waiting_for_cache_choice'})
        elif result == 'manual_analysis_required':
            return jsonify({'success': True, 'message': '等待手动分析数据提交', 'status': 'waiting_for_analysis'})
        else:
            return jsonify({'success': False, 'message': '启动处理任务失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'启动失败: {str(e)}'})

@app.route('/api/preprocessor/status')
@admin_required
def api_preprocessor_status():
    """获取预处理状态API"""
    status = preprocessor_manager.get_processing_status()
    return jsonify(status)

@app.route('/api/preprocessor/remove-file', methods=['POST'])
@admin_required
def api_preprocessor_remove_file():
    """移除文件API"""
    try:
        data = request.get_json()
        filename = data.get('filename')
        if not filename:
            return jsonify({'success': False, 'message': '文件名不能为空'})

        result = preprocessor_manager.remove_file_from_config(filename)
        if result:
            return jsonify({'success': True, 'message': f'文件 {filename} 已移除'})
        else:
            return jsonify({'success': False, 'message': '移除文件失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'移除失败: {str(e)}'})

@app.route('/api/preprocessor/upload', methods=['POST'])
@admin_required
def api_preprocessor_upload():
    """文件上传API"""
    try:
        if 'files' not in request.files:
            return jsonify({'success': False, 'message': '没有选择文件'})

        files = request.files.getlist('files')
        uploaded_count = 0
        skipped_count = 0
        error_count = 0

        # 获取是否允许覆盖的参数，默认为True
        overwrite = request.form.get('overwrite', 'true').lower() == 'true'

        for file in files:
            if file.filename == '':
                continue

            if file and file.filename.endswith('.docx'):
                file_data = file.read()
                result = preprocessor_manager.upload_file(file_data, file.filename, overwrite)
                if result:
                    uploaded_count += 1
                else:
                    # 检查是否是因为文件已存在而跳过
                    docx_dir = Config.PREPROCESSOR_CONFIG['docx_dir']
                    file_path = os.path.join(docx_dir, file.filename)
                    if os.path.exists(file_path) and not overwrite:
                        skipped_count += 1
                    else:
                        error_count += 1
            else:
                error_count += 1

        # 构建返回消息
        messages = []
        if uploaded_count > 0:
            messages.append(f'成功上传 {uploaded_count} 个文件')
        if skipped_count > 0:
            messages.append(f'跳过 {skipped_count} 个已存在文件')
        if error_count > 0:
            messages.append(f'{error_count} 个文件上传失败')

        if uploaded_count > 0:
            return jsonify({'success': True, 'message': '; '.join(messages)})
        else:
            return jsonify({'success': False, 'message': '; '.join(messages) if messages else '没有文件被上传'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'上传失败: {str(e)}'})

@app.route('/api/uploader/start', methods=['POST'])
@admin_required
def api_uploader_start():
    """启动数据库上传任务API"""
    try:
        data = request.get_json()
        json_data = data.get('data')

        if not json_data:
            return jsonify({'success': False, 'message': '没有提供数据'})

        result = uploader_manager.upload_to_database(json_data)
        if result:
            return jsonify({'success': True, 'message': '上传任务已启动'})
        else:
            return jsonify({'success': False, 'message': '启动上传任务失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'启动失败: {str(e)}'})

@app.route('/api/uploader/status')
@admin_required
def api_uploader_status():
    """获取上传状态API"""
    status = uploader_manager.get_upload_status()
    return jsonify(status)

@app.route('/api/uploader/processed-files')
@admin_required
def api_uploader_processed_files():
    """获取已处理文件列表API"""
    try:
        files = uploader_manager.get_processed_files()
        return jsonify({'success': True, 'files': files})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取文件列表失败: {str(e)}'})

@app.route('/api/uploader/load-processed-file')
@admin_required
def api_uploader_load_processed_file():
    """加载已处理文件API"""
    try:
        filename = request.args.get('filename')
        if not filename:
            return jsonify({'success': False, 'message': '文件名不能为空'})

        data = uploader_manager.load_processed_file(filename)
        if data is not None:
            return jsonify({'success': True, 'data': data})
        else:
            return jsonify({'success': False, 'message': '加载文件失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'加载失败: {str(e)}'})

@app.route('/api/pusher/preview', methods=['POST'])
@admin_required
def api_pusher_preview():
    """预览随机题目API"""
    try:
        data = request.get_json() or {}
        tag = data.get('tag')

        question = pusher_manager.preview_random_question(tag)
        if question:
            return jsonify({'success': True, 'question': question})
        else:
            return jsonify({'success': False, 'message': '没有找到符合条件的题目'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'预览失败: {str(e)}'})

@app.route('/api/pusher/send', methods=['POST'])
@admin_required
def api_pusher_send():
    """发送推送API"""
    try:
        data = request.get_json()
        question = data.get('question')
        custom_title = data.get('title')

        if not question:
            return jsonify({'success': False, 'message': '没有提供题目数据'})

        result = pusher_manager.send_push_notification(question, custom_title)
        if result:
            return jsonify({'success': True, 'message': '推送发送成功'})
        else:
            return jsonify({'success': False, 'message': '推送发送失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'发送失败: {str(e)}'})

@app.route('/api/pusher/test')
@admin_required
def api_pusher_test():
    """测试推送服务API"""
    try:
        result = pusher_manager.test_push_service()
        if result:
            return jsonify({'success': True, 'message': '测试推送发送成功'})
        else:
            return jsonify({'success': False, 'message': '测试推送发送失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'测试失败: {str(e)}'})

@app.route('/api/pusher/tags')
@admin_required
def api_pusher_tags():
    """获取可用标签API"""
    try:
        tags = pusher_manager.get_available_tags()
        return jsonify({'success': True, 'tags': tags})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取标签失败: {str(e)}'})

@app.route('/api/pusher/stats')
@admin_required
def api_pusher_stats():
    """获取题目统计API"""
    try:
        stats = pusher_manager.get_question_stats()
        return jsonify({'success': True, 'stats': stats})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取统计失败: {str(e)}'})

@app.route('/api/pusher/history')
@admin_required
def api_pusher_history():
    """获取推送历史API"""
    try:
        history = pusher_manager.get_push_history()
        return jsonify({'success': True, 'history': history})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取历史失败: {str(e)}'})

@app.route('/api/papers')
def api_papers():
    """获取所有试卷API"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cursor = connection.cursor()
        cursor.execute("""
            SELECT p.paper_id, p.paper_name, p.source_file, COUNT(q.id) as question_count
            FROM papers p
            LEFT JOIN questions q ON p.paper_id = q.paper_id
            GROUP BY p.paper_id, p.paper_name, p.source_file
            ORDER BY p.paper_name
        """)

        papers = []
        for row in cursor.fetchall():
            papers.append({
                'id': row['paper_id'],
                'title': row['paper_name'],
                'description': row['source_file'],
                'question_count': row['question_count']
            })

        connection.close()
        return jsonify({'success': True, 'papers': papers})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取试卷失败: {str(e)}'})

@app.route('/api/papers/<int:paper_id>/questions')
def api_paper_questions(paper_id):
    """获取试卷中的题目API"""
    try:
        connection = get_db_connection()
        if not connection:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cursor = connection.cursor()
        cursor.execute("""
            SELECT q.id, q.question_text, q.is_subjective, q.tags
            FROM questions q
            WHERE q.paper_id = %s
            ORDER BY q.id
        """, (paper_id,))

        questions = []
        for row in cursor.fetchall():
            questions.append({
                'id': row['id'],
                'content': row['question_text'],
                'is_subjective': bool(row['is_subjective']),
                'tags': row['tags']
            })

        connection.close()
        return jsonify({'success': True, 'questions': questions})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取题目失败: {str(e)}'})

@app.route('/api/questions/<int:question_id>')
def api_question_detail(question_id):
    """获取题目详情API"""
    try:
        # 获取可选的试卷ID参数，用于验证题目是否属于指定试卷
        paper_id = request.args.get('paper_id', type=int)

        connection = get_db_connection()
        if not connection:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cursor = connection.cursor()

        # 如果提供了paper_id，则验证题目是否属于该试卷
        if paper_id:
            cursor.execute("""
                SELECT q.id, q.question_text, q.options, q.correct_answer, q.analysis,
                       q.is_subjective, q.tags, q.source, q.image_paths, q.shared_materials,
                       p.paper_name, p.image_folder
                FROM questions q
                LEFT JOIN papers p ON q.paper_id = p.paper_id
                WHERE q.id = %s AND q.paper_id = %s
            """, (question_id, paper_id))
        else:
            cursor.execute("""
                SELECT q.id, q.question_text, q.options, q.correct_answer, q.analysis,
                       q.is_subjective, q.tags, q.source, q.image_paths, q.shared_materials,
                       p.paper_name, p.image_folder
                FROM questions q
                LEFT JOIN papers p ON q.paper_id = p.paper_id
                WHERE q.id = %s
            """, (question_id,))

        row = cursor.fetchone()
        if not row:
            connection.close()
            if paper_id:
                return jsonify({'success': False, 'message': f'题目不存在或不属于指定试卷'})
            else:
                return jsonify({'success': False, 'message': '题目不存在'})

        question = {
            'id': row['id'],
            'content': row['question_text'],
            'question_text': row['question_text'],  # 保持与标签模式一致
            'options': row['options'],
            'correct_answer': row['correct_answer'],
            'explanation': row['analysis'],
            'analysis': row['analysis'],  # 保持与标签模式一致
            'is_subjective': bool(row['is_subjective']),
            'tags': row['tags'],
            'source': row['source'],
            'image_paths': row['image_paths'],
            'shared_materials': row['shared_materials'],
            'paper_name': row['paper_name'],
            'image_folder': row['image_folder']
        }

        # 将JSON字符串转换为Python对象（与标签模式保持一致）
        for field in ['options', 'tags', 'image_paths']:
            if question.get(field) and isinstance(question[field], str):
                try:
                    question[field] = json.loads(question[field])
                except json.JSONDecodeError:
                    logger.warning(f"题目ID {question_id} 的 {field} 字段JSON解析失败")
                    if field == 'options':
                        question[field] = []
                    elif field == 'tags':
                        question[field] = []
                    elif field == 'image_paths':
                        question[field] = []

        connection.close()
        return jsonify({'success': True, 'question': question})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取题目详情失败: {str(e)}'})

@app.route('/api/preprocessor/cache-choice', methods=['POST'])
@admin_required
def api_preprocessor_cache_choice():
    """处理用户的缓存选择API"""
    try:
        data = request.get_json()
        document_name = data.get('document_name')
        choice = data.get('choice')  # 'use_cache', 'reanalyze', 'edit_cache'

        if not document_name or not choice:
            return jsonify({'success': False, 'message': '缺少必要参数'})

        if choice == 'use_cache':
            # 使用缓存继续处理
            result = preprocessor_manager.continue_processing_with_cache(document_name)
            if result:
                return jsonify({'success': True, 'message': '使用缓存继续处理...'})
            else:
                return jsonify({'success': False, 'message': '处理失败'})

        elif choice == 'reanalyze':
            # 重新分析，清除缓存
            result = preprocessor_manager.restart_analysis(document_name)
            if result == 'manual_analysis_required':
                return jsonify({'success': True, 'message': '开始重新分析...', 'status': 'waiting_for_analysis'})
            else:
                return jsonify({'success': False, 'message': '重新分析失败'})

        elif choice == 'edit_cache':
            # 编辑缓存，显示JSON编辑界面
            cache_data = preprocessor_manager.get_cache_data(document_name)
            if cache_data:
                return jsonify({
                    'success': True,
                    'message': '准备编辑缓存...',
                    'status': 'edit_cache',
                    'cache_data': cache_data
                })
            else:
                return jsonify({'success': False, 'message': '无法获取缓存数据'})
        else:
            return jsonify({'success': False, 'message': '无效的选择'})

    except Exception as e:
        logger.error(f"处理缓存选择时发生错误: {e}")
        return jsonify({'success': False, 'message': f'处理失败: {str(e)}'})

@app.route('/api/preprocessor/submit-analysis', methods=['POST'])
@admin_required
def api_preprocessor_submit_analysis():
    """提交分析数据API"""
    try:
        data = request.get_json()
        document_name = data.get('document_name')
        analysis_data = data.get('analysis_data')

        if not document_name or not analysis_data:
            return jsonify({'success': False, 'message': '文档名称和分析数据不能为空'})

        # 保存分析数据到缓存文件
        result = preprocessor_manager.save_analysis_data(document_name, analysis_data)

        if result:
            return jsonify({'success': True, 'message': '分析数据保存成功'})
        else:
            return jsonify({'success': False, 'message': '保存分析数据失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'提交失败: {str(e)}'})

@app.route('/api/preprocessor/continue-processing', methods=['POST'])
@admin_required
def api_preprocessor_continue_processing():
    """继续处理文档API"""
    try:
        data = request.get_json()
        document_name = data.get('document_name')

        if not document_name:
            return jsonify({'success': False, 'message': '文档名称不能为空'})

        # 在新的事件循环中运行异步任务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(preprocessor_manager.continue_processing(document_name))
        loop.close()

        if result:
            return jsonify({'success': True, 'message': '文档处理完成'})
        else:
            return jsonify({'success': False, 'message': '文档处理失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'处理失败: {str(e)}'})

@app.route('/api/pusher/config', methods=['GET', 'POST'])
@admin_required
def api_pusher_config():
    """推送配置API"""
    if request.method == 'GET':
        # 获取推送配置
        return jsonify({
            'success': True,
            'config': pusher_manager.get_push_config()
        })

    elif request.method == 'POST':
        # 更新推送配置
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': '请提供配置数据'})

            success = pusher_manager.update_push_config(data)
            if success:
                return jsonify({'success': True, 'message': '配置更新成功'})
            else:
                return jsonify({'success': False, 'message': '配置更新失败'})
        except Exception as e:
            return jsonify({'success': False, 'message': f'配置更新失败: {str(e)}'})


def create_download_package(document_name: str) -> str:
    """创建下载包（ZIP文件）"""
    from modules.document_processor import get_doc_directories

    work_dir = Config.PREPROCESSOR_CONFIG['work_dir']
    directories = get_doc_directories(document_name, work_dir)

    # 创建临时ZIP文件
    temp_dir = tempfile.mkdtemp()
    zip_path = os.path.join(temp_dir, f"{document_name}_analysis_results.zip")

    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # 添加JSON文件
        json_file = os.path.join(directories["output_dir"], f"{document_name}.json")
        if os.path.exists(json_file):
            zipf.write(json_file, f"json/{document_name}.json")

        # 添加处理后的文本文件
        txt_file = os.path.join(directories["output_dir"], f"{document_name}_processed.txt")
        if os.path.exists(txt_file):
            zipf.write(txt_file, f"processed/{document_name}_processed.txt")

        # 添加缓存文件（分析数据）
        cache_file = os.path.join(directories["cache_dir"], f"{document_name}_analysis_cache.json")
        if os.path.exists(cache_file):
            zipf.write(cache_file, f"analysis/{document_name}_analysis_cache.json")

        # 添加图片文件
        img_dir = directories["img_dir"]
        if os.path.exists(img_dir):
            for root, dirs, files in os.walk(img_dir):
                for file in files:
                    if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp')):
                        file_path = os.path.join(root, file)
                        # 保持相对路径结构
                        arcname = os.path.join("images", os.path.relpath(file_path, img_dir))
                        zipf.write(file_path, arcname)

        # 添加表格文件
        tables_dir = directories["tables_dir"]
        if os.path.exists(tables_dir):
            for root, dirs, files in os.walk(tables_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 保持相对路径结构
                    arcname = os.path.join("tables", os.path.relpath(file_path, tables_dir))
                    zipf.write(file_path, arcname)

        # 添加README文件
        readme_content = f"""# {document_name} 分析结果

## 文件说明

### json/
- `{document_name}.json`: 最终生成的题库JSON文件，可直接导入数据库

### processed/
- `{document_name}_processed.txt`: AI处理后的格式化文本文件

### analysis/
- `{document_name}_analysis_cache.json`: 文档结构分析数据，包含题组信息和图片分析

### images/
- 从原始文档中提取的所有图片文件
- 图片按照在文档中的顺序命名（image1.png, image2.png, ...）

### tables/
- 从文档中提取的表格文件（如果有）
- 表格以Excel格式保存

## 使用说明

1. **题库导入**: 使用 `json/{document_name}.json` 文件可直接导入到地理题库系统
2. **图片资源**: `images/` 目录中的图片需要上传到服务器的图片目录
3. **分析数据**: `analysis/` 目录中的文件包含了完整的文档分析信息，可用于后续处理

## 生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        # 将README内容写入ZIP
        zipf.writestr("README.md", readme_content.encode('utf-8'))

    return zip_path

@app.route('/api/preprocessor/download/<document_name>')
def api_preprocessor_download(document_name):
    """下载预处理结果API"""
    try:
        # 验证文档名称
        if not document_name or '..' in document_name or '/' in document_name:
            return jsonify({'success': False, 'message': '无效的文档名称'}), 400

        # 检查文档是否存在处理结果
        from modules.document_processor import get_doc_directories
        work_dir = Config.PREPROCESSOR_CONFIG['work_dir']
        directories = get_doc_directories(document_name, work_dir)

        # 检查是否有处理结果
        json_file = os.path.join(directories["output_dir"], f"{document_name}.json")
        if not os.path.exists(json_file):
            return jsonify({'success': False, 'message': '该文档尚未完成处理或处理结果不存在'}), 404

        # 创建下载包
        zip_path = create_download_package(document_name)

        # 发送文件
        return send_file(
            zip_path,
            as_attachment=True,
            download_name=f"{document_name}_analysis_results.zip",
            mimetype='application/zip'
        )

    except Exception as e:
        logger.error(f"下载失败: {e}")
        return jsonify({'success': False, 'message': f'下载失败: {str(e)}'}), 500

@app.route('/api/preprocessor/download-list')
def api_preprocessor_download_list():
    """获取可下载的文档列表API"""
    try:
        from modules.document_processor import get_doc_directories
        work_dir = Config.PREPROCESSOR_CONFIG['work_dir']

        available_downloads = []

        # 扫描工作目录中的所有文档
        if os.path.exists(work_dir):
            for item in os.listdir(work_dir):
                item_path = os.path.join(work_dir, item)
                if os.path.isdir(item_path):
                    # 检查是否有完成的处理结果
                    directories = get_doc_directories(item, work_dir)
                    json_file = os.path.join(directories["output_dir"], f"{item}.json")

                    if os.path.exists(json_file):
                        # 获取文件信息
                        file_stat = os.stat(json_file)
                        file_size = file_stat.st_size
                        modified_time = datetime.fromtimestamp(file_stat.st_mtime)

                        # 统计包含的文件数量
                        file_count = 0
                        total_size = 0

                        # 统计各类文件
                        for dir_key, dir_path in directories.items():
                            if os.path.exists(dir_path):
                                for root, dirs, files in os.walk(dir_path):
                                    for file in files:
                                        file_path = os.path.join(root, file)
                                        if os.path.isfile(file_path):
                                            file_count += 1
                                            total_size += os.path.getsize(file_path)

                        available_downloads.append({
                            'document_name': item,
                            'json_file_size': file_size,
                            'total_files': file_count,
                            'total_size': total_size,
                            'modified_time': modified_time.strftime('%Y-%m-%d %H:%M:%S'),
                            'download_url': f'/api/preprocessor/download/{item}'
                        })

        return jsonify({
            'success': True,
            'downloads': available_downloads
        })

    except Exception as e:
        logger.error(f"获取下载列表失败: {e}")
        return jsonify({'success': False, 'message': f'获取下载列表失败: {str(e)}'}), 500

# WebSocket事件处理
@socketio.on('connect')
def handle_connect():
    """客户端连接事件"""
    logger.info('客户端已连接')
    emit('status', {'message': '已连接到服务器'})

@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接事件"""
    logger.info('客户端已断开连接')

def emit_log(message, level='info'):
    """发送日志消息到前端"""
    socketio.emit('log', {
        'message': message,
        'level': level,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

if __name__ == '__main__':
    # 根据环境变量决定运行模式
    import os
    import socket
    env = os.environ.get('FLASK_ENV', 'development')
    debug_mode = env == 'development'

    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5002))

    # 检查端口是否可用，如果不可用则自动寻找下一个可用端口
    def find_free_port(start_port, host='0.0.0.0'):
        """寻找可用端口"""
        for port_num in range(start_port, start_port + 10):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                    s.bind((host, port_num))
                    return port_num
            except OSError:
                continue
        return None

    # 寻找可用端口
    available_port = find_free_port(port, host)
    if available_port is None:
        print(f"❌ 无法找到可用端口（尝试范围：{port}-{port+9}）")
        exit(1)

    if available_port != port:
        print(f"⚠️  端口 {port} 被占用，使用端口 {available_port}")
        port = available_port

    print(f"🚀 启动地理题库系统 (环境: {env})")
    print(f"📍 访问地址: http://{host}:{port}")

    try:
        socketio.run(app, debug=debug_mode, host=host, port=port, allow_unsafe_werkzeug=True)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 建议：")
        print("   1. 检查是否有其他进程占用端口")
        print("   2. 尝试使用不同的端口")
        print("   3. 重启计算机释放所有端口")
        exit(1)
