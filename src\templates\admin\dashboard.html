{% extends "base.html" %}

{% block title %}仪表板 - 地理题库管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tachometer-alt"></i> 系统仪表板</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" onclick="refreshStats()">
            <i class="fas fa-sync-alt"></i> 刷新数据
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number" id="papers-count">
                {% if stats %}{{ stats.papers_count }}{% else %}--{% endif %}
            </div>
            <div class="stats-label">
                <i class="fas fa-file-alt"></i> 试卷总数
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="stats-number" id="questions-count">
                {% if stats %}{{ stats.questions_count }}{% else %}--{% endif %}
            </div>
            <div class="stats-label">
                <i class="fas fa-question-circle"></i> 题目总数
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="stats-number" id="connection-status-count">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-label">
                <i class="fas fa-database"></i> 数据库状态
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="stats-number" id="system-status">
                <i class="fas fa-server"></i>
            </div>
            <div class="stats-label">
                <i class="fas fa-cogs"></i> 系统状态
            </div>
        </div>
    </div>
</div>

<!-- 功能模块卡片 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-file-word"></i> 文档预处理
            </div>
            <div class="card-body">
                <p class="card-text">上传和处理Word文档，提取地理题目并进行结构化处理。</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> 支持.docx格式</li>
                    <li><i class="fas fa-check text-success"></i> AI智能分析</li>
                    <li><i class="fas fa-check text-success"></i> 图片提取</li>
                    <li><i class="fas fa-check text-success"></i> 缓存机制</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('admin_preprocessor') }}" class="btn btn-primary w-100">
                    <i class="fas fa-arrow-right"></i> 进入模块
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-database"></i> 数据库管理
            </div>
            <div class="card-body">
                <p class="card-text">将处理后的题目数据上传到MySQL数据库，管理试卷和题目。</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> 批量上传</li>
                    <li><i class="fas fa-check text-success"></i> 数据验证</li>
                    <li><i class="fas fa-check text-success"></i> 重复检测</li>
                    <li><i class="fas fa-check text-success"></i> 统计分析</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('admin_uploader') }}" class="btn btn-primary w-100">
                    <i class="fas fa-arrow-right"></i> 进入模块
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-paper-plane"></i> 推送管理
            </div>
            <div class="card-body">
                <p class="card-text">从数据库随机抽取题目，生成HTML格式并推送到指定平台。</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> 随机抽题</li>
                    <li><i class="fas fa-check text-success"></i> HTML生成</li>
                    <li><i class="fas fa-check text-success"></i> 多平台推送</li>
                    <li><i class="fas fa-check text-success"></i> 定时任务</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('admin_pusher') }}" class="btn btn-primary w-100">
                    <i class="fas fa-arrow-right"></i> 进入模块
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-history"></i> 最近添加的试卷
            </div>
            <div class="card-body">
                {% if stats and stats.recent_papers %}
                    <div class="list-group list-group-flush">
                        {% for paper in stats.recent_papers %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ paper.paper_name }}</strong>
                                <br>
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> 
                                    {{ paper.created_at.strftime('%Y-%m-%d %H:%M') if paper.created_at else '未知时间' }}
                                </small>
                            </div>
                            <span class="badge bg-primary rounded-pill">新</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">暂无数据</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-terminal"></i> 系统日志
            </div>
            <div class="card-body">
                <div id="log-container" class="log-container">
                    <div class="log-entry log-info">
                        <span class="log-timestamp">[系统启动]</span>
                        系统启动完成，等待操作...
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">
                    <i class="fas fa-trash"></i> 清空日志
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshStats() {
    fetch('/api/database/stats')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('获取统计数据失败:', data.error);
                return;
            }

            // 更新统计数据
            document.getElementById('papers-count').textContent = data.papers_count || '--';
            document.getElementById('questions-count').textContent = data.questions_count || '--';

            // 更新最近添加的试卷列表
            updateRecentPapers(data.recent_papers);
        })
        .catch(error => {
            console.error('请求失败:', error);
        });
}

function updateRecentPapers(recentPapers) {
    const recentPapersContainer = document.querySelector('.col-md-6 .card .card-body');

    if (!recentPapers || recentPapers.length === 0) {
        recentPapersContainer.innerHTML = '<p class="text-muted">暂无数据</p>';
        return;
    }

    let papersHtml = '<div class="list-group list-group-flush">';
    recentPapers.forEach(paper => {
        const createdAt = paper.created_at ? new Date(paper.created_at).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }) : '未知时间';

        papersHtml += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <strong>${paper.paper_name}</strong>
                    <br>
                    <small class="text-muted">
                        <i class="fas fa-calendar"></i>
                        ${createdAt}
                    </small>
                </div>
                <span class="badge bg-primary rounded-pill">新</span>
            </div>
        `;
    });
    papersHtml += '</div>';

    recentPapersContainer.innerHTML = papersHtml;
}

function clearLogs() {
    const logContainer = document.getElementById('log-container');
    logContainer.innerHTML = '<div class="log-entry log-info"><span class="log-timestamp">[' +
        new Date().toLocaleString() + ']</span>日志已清空</div>';
}

// 页面加载时和定期刷新统计数据
document.addEventListener('DOMContentLoaded', function() {
    refreshStats(); // 页面加载时立即刷新一次

    // 每30秒自动刷新一次统计数据
    setInterval(refreshStats, 30000);
});

// 页面加载时测试数据库连接
document.addEventListener('DOMContentLoaded', function() {
    fetch('/api/test-connection')
        .then(response => response.json())
        .then(data => {
            const statusElement = document.getElementById('connection-status-count');
            if (data.success) {
                statusElement.innerHTML = '<i class="fas fa-check-circle"></i>';
                statusElement.parentElement.style.background = 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)';
            } else {
                statusElement.innerHTML = '<i class="fas fa-times-circle"></i>';
                statusElement.parentElement.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
            }
        })
        .catch(error => {
            console.error('连接测试失败:', error);
            const statusElement = document.getElementById('connection-status-count');
            statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            statusElement.parentElement.style.background = 'linear-gradient(135deg, #ffa726 0%, #ff7043 100%)';
        });
});
</script>
{% endblock %}
