-- 地理题库系统数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS geo_questions CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE geo_questions;

-- 创建试卷表
CREATE TABLE IF NOT EXISTS papers (
    paper_id VARCHAR(50) PRIMARY KEY,
    paper_name VARCHAR(255) NOT NULL,
    source_file VARCHAR(255),
    image_folder VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_paper_name (paper_name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建题目表
CREATE TABLE IF NOT EXISTS questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    paper_id VARCHAR(50),
    group_id VARCHAR(50),
    is_group_header BOOLEAN DEFAULT FALSE,
    question_text TEXT NOT NULL,
    shared_materials TEXT,
    options JSON,
    correct_answer TEXT,
    analysis TEXT,
    is_subjective BOOLEAN DEFAULT FALSE,
    tags JSON,
    source VARCHAR(255),
    image_paths JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (paper_id) REFERENCES papers(paper_id) ON DELETE CASCADE,
    INDEX idx_paper_id (paper_id),
    INDEX idx_group_id (group_id),
    INDEX idx_is_subjective (is_subjective),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_question_text (question_text),
    FULLTEXT idx_analysis (analysis)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户表（增强版，支持角色管理）
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role VARCHAR(20) DEFAULT 'user' NOT NULL,  -- 'admin' 或 'user'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建答题记录表
CREATE TABLE IF NOT EXISTS user_answers (
    answer_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    question_id INT NOT NULL,  -- 关联questions表的id字段
    user_answer TEXT,
    is_correct BOOLEAN NOT NULL,
    time_spent INT DEFAULT 0,  -- 答题用时，单位：秒
    answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_question_id (question_id),
    INDEX idx_answered_at (answered_at),
    INDEX idx_is_correct (is_correct)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建题目统计表
CREATE TABLE IF NOT EXISTS question_stats (
    question_id INT PRIMARY KEY,  -- 关联questions表的id字段
    total_attempts INT DEFAULT 0,
    correct_attempts INT DEFAULT 0,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    INDEX idx_total_attempts (total_attempts),
    INDEX idx_correct_attempts (correct_attempts)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户收藏表
CREATE TABLE IF NOT EXISTS user_favorites (
    favorite_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    question_id INT NOT NULL,  -- 关联questions表的id字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_question (user_id, question_id),  -- 防止重复收藏
    INDEX idx_user_id (user_id),
    INDEX idx_question_id (question_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建推送历史表
CREATE TABLE IF NOT EXISTS push_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    question_id INT,
    title VARCHAR(255),
    content_preview TEXT,
    push_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('success', 'failed') DEFAULT 'success',
    error_message TEXT,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE SET NULL,
    INDEX idx_question_id (question_id),
    INDEX idx_push_time (push_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认配置
INSERT IGNORE INTO system_config (config_key, config_value, description) VALUES
('system_version', '1.0.0', '系统版本'),
('default_push_mode', 'simple', '默认推送模式'),
('max_image_size', '10485760', '最大图片大小（字节）'),
('allowed_image_types', '["jpg", "jpeg", "png", "gif", "bmp", "webp"]', '允许的图片类型');

-- 创建默认管理员用户（密码: admin123，请在生产环境中修改）
INSERT IGNORE INTO users (username, email, password_hash, role) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u', 'admin');

-- 创建示例试卷（可选）
INSERT IGNORE INTO papers (paper_id, paper_name, source_file, image_folder) VALUES
('sample_001', '示例试卷', 'sample.docx', 'sample');

-- 创建示例题目（可选）
INSERT IGNORE INTO questions (paper_id, question_text, options, correct_answer, analysis, is_subjective, tags) VALUES
('sample_001', '地球的自转方向是？', 
 '[{"key": "A", "value": "自西向东"}, {"key": "B", "value": "自东向西"}, {"key": "C", "value": "自南向北"}, {"key": "D", "value": "自北向南"}]',
 'A', '地球自转方向是自西向东，这是地球运动的基本特征。', FALSE, '["地球运动", "自转"]');

-- 优化数据库性能
-- 设置合适的字符集和排序规则
ALTER DATABASE geo_questions CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建复合索引以提高查询性能
CREATE INDEX idx_questions_paper_group ON questions(paper_id, group_id);
CREATE INDEX idx_questions_subjective_tags ON questions(is_subjective, tags(100));

-- 设置MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = 268435456;  -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864;  -- 64MB
