"""
用户刷题应用蓝图
处理刷题相关的所有功能和API
"""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from flask_login import login_required, current_user
from .models import User, UserAnswer, QuestionStats, UserFavorite
from .database import DatabaseManager
from .decorators import user_required
import logging
import json
import random
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

# 创建刷题蓝图
quiz_bp = Blueprint('quiz', __name__, url_prefix='/quiz')

# 数据库管理器实例（需要在应用初始化时设置）
db_manager = None


def init_quiz(database_manager: DatabaseManager):
    """初始化刷题模块"""
    global db_manager
    db_manager = database_manager


@quiz_bp.route('/home')
@user_required
def home():
    """刷题主页"""
    return render_template('quiz/home.html')


@quiz_bp.route('/quiz')
@user_required
def quiz_page():
    """答题界面"""
    return render_template('quiz/quiz.html')


@quiz_bp.route('/profile')
@user_required
def profile():
    """个人中心"""
    return render_template('quiz/profile.html')


@quiz_bp.route('/api/tags')
@user_required
def api_tags():
    """获取所有题目的知识点标签列表"""
    if not db_manager:
        return jsonify({'success': False, 'message': '数据库连接失败'})
    
    connection = db_manager.get_connection()
    if not connection:
        return jsonify({'success': False, 'message': '数据库连接失败'})
    
    try:
        cursor = connection.cursor()
        cursor.execute("""
            SELECT DISTINCT JSON_UNQUOTE(JSON_EXTRACT(tags, '$[*]')) as tag
            FROM questions 
            WHERE tags IS NOT NULL AND JSON_LENGTH(tags) > 0
        """)
        
        # 处理标签数据
        tags = set()
        for row in cursor.fetchall():
            if row['tag']:
                # 处理可能的多个标签
                tag_list = json.loads(row['tag']) if isinstance(row['tag'], str) else [row['tag']]
                for tag in tag_list:
                    if tag and tag.strip():
                        tags.add(tag.strip())
        
        return jsonify({
            'success': True,
            'tags': sorted(list(tags))
        })
    except Exception as e:
        logger.error(f"获取标签失败: {e}")
        return jsonify({'success': False, 'message': f'获取标签失败: {str(e)}'})
    finally:
        connection.close()


@quiz_bp.route('/api/question')
@user_required
def api_question():
    """获取一道题目"""
    if not db_manager:
        return jsonify({'success': False, 'message': '数据库连接失败'})
    
    connection = db_manager.get_connection()
    if not connection:
        return jsonify({'success': False, 'message': '数据库连接失败'})
    
    try:
        cursor = connection.cursor()
        mode = request.args.get('mode', 'random')  # random 或 tag
        tag = request.args.get('tag', '')
        
        # 构建查询条件
        if mode == 'tag' and tag:
            # 按标签查询
            cursor.execute("""
                SELECT id, question_text, options, correct_answer, analysis, 
                       tags, image_paths, is_subjective
                FROM questions 
                WHERE JSON_CONTAINS(tags, %s)
                ORDER BY RAND()
                LIMIT 1
            """, (json.dumps(tag),))
        else:
            # 随机查询
            cursor.execute("""
                SELECT id, question_text, options, correct_answer, analysis, 
                       tags, image_paths, is_subjective
                FROM questions 
                WHERE is_subjective = 0  -- 只获取客观题
                ORDER BY RAND()
                LIMIT 1
            """)
        
        question = cursor.fetchone()
        if not question:
            return jsonify({'success': False, 'message': '没有找到符合条件的题目'})
        
        # 检查用户是否收藏了这道题
        cursor.execute("""
            SELECT 1 FROM user_favorites 
            WHERE user_id = %s AND question_id = %s
        """, (current_user.user_id, question['id']))
        
        is_favorited = cursor.fetchone() is not None
        
        # 处理选项数据
        options = json.loads(question['options']) if question['options'] else []
        tags = json.loads(question['tags']) if question['tags'] else []
        image_paths = json.loads(question['image_paths']) if question['image_paths'] else []
        
        return jsonify({
            'success': True,
            'question': {
                'id': question['id'],
                'question_text': question['question_text'],
                'options': options,
                'tags': tags,
                'image_paths': image_paths,
                'is_subjective': bool(question['is_subjective']),
                'is_favorited': is_favorited
            }
        })
    except Exception as e:
        logger.error(f"获取题目失败: {e}")
        return jsonify({'success': False, 'message': f'获取题目失败: {str(e)}'})
    finally:
        connection.close()


@quiz_bp.route('/api/answer', methods=['POST'])
@user_required
def api_answer():
    """接收用户提交的答案"""
    if not db_manager:
        return jsonify({'success': False, 'message': '数据库连接失败'})
    
    try:
        data = request.get_json()
        question_id = data.get('question_id')
        user_answer = data.get('user_answer', '')
        time_spent = data.get('time_spent', 0)
        
        if not question_id:
            return jsonify({'success': False, 'message': '题目ID不能为空'})
        
        connection = db_manager.get_connection()
        if not connection:
            return jsonify({'success': False, 'message': '数据库连接失败'})
        
        cursor = connection.cursor()
        
        # 获取题目信息
        cursor.execute("""
            SELECT correct_answer, analysis, question_text, options
            FROM questions WHERE id = %s
        """, (question_id,))
        
        question = cursor.fetchone()
        if not question:
            return jsonify({'success': False, 'message': '题目不存在'})
        
        # 判断答案是否正确
        correct_answer = question['correct_answer']
        is_correct = user_answer.strip().upper() == correct_answer.strip().upper()
        
        # 记录答题记录
        answer_record = UserAnswer.create(
            user_id=current_user.user_id,
            question_id=question_id,
            user_answer=user_answer,
            is_correct=is_correct,
            time_spent=time_spent,
            db_manager=db_manager
        )
        
        if answer_record:
            # 更新题目统计
            QuestionStats.update_stats(question_id, is_correct, db_manager)
            
            return jsonify({
                'success': True,
                'is_correct': is_correct,
                'correct_answer': correct_answer,
                'analysis': question['analysis'],
                'user_answer': user_answer
            })
        else:
            return jsonify({'success': False, 'message': '记录答题失败'})
            
    except Exception as e:
        logger.error(f"处理答题失败: {e}")
        return jsonify({'success': False, 'message': f'处理答题失败: {str(e)}'})
    finally:
        if 'connection' in locals():
            connection.close()


@quiz_bp.route('/api/favorite', methods=['POST'])
@user_required
def api_favorite():
    """添加/取消题目收藏"""
    try:
        data = request.get_json()
        question_id = data.get('question_id')
        
        if not question_id:
            return jsonify({'success': False, 'message': '题目ID不能为空'})
        
        result = UserFavorite.toggle_favorite(
            user_id=current_user.user_id,
            question_id=question_id,
            db_manager=db_manager
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"切换收藏状态失败: {e}")
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})


@quiz_bp.route('/api/history')
@user_required
def api_history():
    """获取用户的答题历史记录"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        history = UserAnswer.get_user_history(
            user_id=current_user.user_id,
            page=page,
            per_page=per_page,
            db_manager=db_manager
        )
        
        # 转换为JSON格式
        history_data = []
        for record in history:
            history_data.append({
                'answer_id': record.answer_id,
                'question_id': record.question_id,
                'question_text': getattr(record, 'question_text', ''),
                'user_answer': record.user_answer,
                'is_correct': record.is_correct,
                'time_spent': record.time_spent,
                'answered_at': record.answered_at.isoformat() if record.answered_at else None
            })
        
        return jsonify({
            'success': True,
            'history': history_data,
            'page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        logger.error(f"获取答题历史失败: {e}")
        return jsonify({'success': False, 'message': f'获取历史记录失败: {str(e)}'})


@quiz_bp.route('/api/stats')
@user_required
def api_stats():
    """获取用户的个人统计数据"""
    if not db_manager:
        return jsonify({'success': False, 'message': '数据库连接失败'})
    
    connection = db_manager.get_connection()
    if not connection:
        return jsonify({'success': False, 'message': '数据库连接失败'})
    
    try:
        cursor = connection.cursor()
        user_id = current_user.user_id
        
        # 总答题数
        cursor.execute("""
            SELECT COUNT(*) as total_count FROM user_answers WHERE user_id = %s
        """, (user_id,))
        total_count = cursor.fetchone()['total_count']
        
        # 正确答题数
        cursor.execute("""
            SELECT COUNT(*) as correct_count FROM user_answers 
            WHERE user_id = %s AND is_correct = 1
        """, (user_id,))
        correct_count = cursor.fetchone()['correct_count']
        
        # 收藏题目数
        cursor.execute("""
            SELECT COUNT(*) as favorite_count FROM user_favorites WHERE user_id = %s
        """, (user_id,))
        favorite_count = cursor.fetchone()['favorite_count']
        
        # 计算正确率
        accuracy_rate = (correct_count / total_count * 100) if total_count > 0 else 0
        
        return jsonify({
            'success': True,
            'stats': {
                'total_count': total_count,
                'correct_count': correct_count,
                'wrong_count': total_count - correct_count,
                'accuracy_rate': round(accuracy_rate, 2),
                'favorite_count': favorite_count
            }
        })
        
    except Exception as e:
        logger.error(f"获取用户统计失败: {e}")
        return jsonify({'success': False, 'message': f'获取统计数据失败: {str(e)}'})
    finally:
        connection.close()


@quiz_bp.route('/api/mistakes')
@user_required
def api_mistakes():
    """获取用户的错题本题目列表"""
    if not db_manager:
        return jsonify({'success': False, 'message': '数据库连接失败'})
    
    connection = db_manager.get_connection()
    if not connection:
        return jsonify({'success': False, 'message': '数据库连接失败'})
    
    try:
        cursor = connection.cursor()
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        offset = (page - 1) * per_page
        
        # 获取错题列表
        cursor.execute("""
            SELECT DISTINCT q.id, q.question_text, q.options, q.correct_answer, 
                   q.analysis, q.tags, ua.answered_at
            FROM user_answers ua
            JOIN questions q ON ua.question_id = q.id
            WHERE ua.user_id = %s AND ua.is_correct = 0
            ORDER BY ua.answered_at DESC
            LIMIT %s OFFSET %s
        """, (current_user.user_id, per_page, offset))
        
        mistakes = []
        for row in cursor.fetchall():
            mistakes.append({
                'id': row['id'],
                'question_text': row['question_text'],
                'options': json.loads(row['options']) if row['options'] else [],
                'correct_answer': row['correct_answer'],
                'analysis': row['analysis'],
                'tags': json.loads(row['tags']) if row['tags'] else [],
                'answered_at': row['answered_at'].isoformat() if row['answered_at'] else None
            })
        
        return jsonify({
            'success': True,
            'mistakes': mistakes,
            'page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        logger.error(f"获取错题本失败: {e}")
        return jsonify({'success': False, 'message': f'获取错题本失败: {str(e)}'})
    finally:
        connection.close()
