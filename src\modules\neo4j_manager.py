"""
Neo4j数据库管理模块
处理知识图谱的数据库连接和查询操作
"""

import logging
from typing import Dict, List, Any, Optional
import json

logger = logging.getLogger(__name__)

class Neo4jManager:
    """Neo4j数据库管理器"""
    
    def __init__(self, uri: str = "neo4j://localhost:7687", user: str = "neo4j", password: str = "12345678"):
        self.uri = uri
        self.user = user
        self.password = password
        self.graph = None
        self._connect()

    def _connect(self):
        """连接到Neo4j数据库"""
        try:
            # 尝试导入py2neo
            from py2neo import Graph
            self.graph = Graph(self.uri, auth=(self.user, self.password))
            # 测试连接
            self.graph.run("RETURN 1").data()
            logger.info(f"成功连接到Neo4j数据库: {self.uri}")
        except ImportError:
            logger.warning("py2neo未安装，使用模拟数据")
            self.graph = None
        except Exception as e:
            logger.error(f"连接Neo4j数据库失败: {e}")
            self.graph = None
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        if not self.graph:
            return False
        try:
            result = self.graph.run("RETURN 1 as test").data()
            return len(result) > 0 and result[0]["test"] == 1
        except Exception as e:
            logger.error(f"Neo4j连接测试失败: {e}")
            return False
    
    def get_graph_data(self, limit: int = 100, node_type_filter: str = '', relationship_type_filter: str = '') -> Dict[str, Any]:
        """获取图谱数据"""
        if not self.graph:
            raise ConnectionError("Neo4j数据库连接不可用，无法获取图谱数据")

        try:
            # 构建动态查询以支持筛选
            where_conditions = []
            params = {"limit": limit}

            # 添加节点类型筛选
            if node_type_filter:
                where_conditions.append("$node_type_filter IN labels(n) OR $node_type_filter IN labels(m)")
                params["node_type_filter"] = node_type_filter

            # 添加关系类型筛选
            if relationship_type_filter:
                where_conditions.append("type(r) = $relationship_type_filter")
                params["relationship_type_filter"] = relationship_type_filter

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            query = f"""
            MATCH (n)-[r]->(m)
            {where_clause}
            RETURN
                id(n) as source_id,
                labels(n) as source_labels,
                properties(n) as source_props,
                type(r) as rel_type,
                properties(r) as rel_props,
                id(m) as target_id,
                labels(m) as target_labels,
                properties(m) as target_props
            LIMIT $limit
            """
            result = self.graph.run(query, **params).data()

            nodes_dict = {}
            edges = []

            for record in result:
                # 处理源节点
                source_id = record["source_id"]
                if source_id not in nodes_dict:
                    nodes_dict[source_id] = {
                        "id": source_id,
                        "label": record["source_labels"][0] if record["source_labels"] else "Node",
                        "labels": record["source_labels"],
                        "properties": dict(record["source_props"]) if record["source_props"] else {}
                    }

                # 处理目标节点
                target_id = record["target_id"]
                if target_id not in nodes_dict:
                    nodes_dict[target_id] = {
                        "id": target_id,
                        "label": record["target_labels"][0] if record["target_labels"] else "Node",
                        "labels": record["target_labels"],
                        "properties": dict(record["target_props"]) if record["target_props"] else {}
                    }

                # 添加关系
                if record["rel_type"]:
                    edges.append({
                        "source": source_id,
                        "target": target_id,
                        "type": record["rel_type"],
                        "properties": dict(record["rel_props"]) if record["rel_props"] else {}
                    })

            nodes = list(nodes_dict.values())

            return {
                "nodes": nodes,
                "edges": edges,
                "stats": {
                    "node_count": len(nodes),
                    "edge_count": len(edges)
                }
            }
        except Exception as e:
            logger.error(f"获取图谱数据失败: {e}")
            raise ConnectionError(f"从Neo4j获取图谱数据失败: {str(e)}")
    
    def search_nodes(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索节点"""
        if not self.graph:
            logger.warning("Neo4j数据库连接不可用，无法搜索节点")
            return []

        try:
            search_query = """
            MATCH (n)
            WHERE any(prop in keys(n) WHERE toString(n[prop]) CONTAINS $query)
               OR any(label in labels(n) WHERE label CONTAINS $query)
            RETURN id(n) as id, labels(n) as labels, properties(n) as properties
            LIMIT $limit
            """
            result = self.graph.run(search_query, query=query, limit=limit).data()

            nodes = []
            for record in result:
                node = {
                    "id": record["id"],
                    "label": record["labels"][0] if record["labels"] else "Node",
                    "labels": record["labels"],
                    "properties": dict(record["properties"])
                }
                nodes.append(node)

            return nodes
        except Exception as e:
            logger.error(f"搜索节点失败: {e}")
            return []
    
    def get_node_details(self, node_id: int) -> Optional[Dict[str, Any]]:
        """获取节点详细信息"""
        if not self.graph:
            logger.warning("Neo4j数据库连接不可用，无法获取节点详情")
            return None

        try:
            query = """
            MATCH (n)
            WHERE id(n) = $node_id
            OPTIONAL MATCH (n)-[r]-(connected)
            RETURN n, collect({
                relationship: r,
                node: connected,
                direction: CASE WHEN startNode(r) = n THEN 'outgoing' ELSE 'incoming' END
            }) as connections
            """
            result = self.graph.run(query, node_id=node_id).data()

            if result:
                record = result[0]
                node = record["n"]
                return {
                    "id": node.identity,
                    "labels": list(node.labels),
                    "properties": dict(node),
                    "connections": record["connections"]
                }
            return None
        except Exception as e:
            logger.error(f"获取节点详情失败: {e}")
            return None
    
    def get_node_types(self) -> List[str]:
        """获取所有节点类型"""
        if not self.graph:
            return ["地理概念", "知识点", "题目", "试卷"]

        try:
            result = self.graph.run("CALL db.labels()").data()
            return [record["label"] for record in result]
        except Exception as e:
            logger.error(f"获取节点类型失败: {e}")
            return ["地理概念", "知识点", "题目", "试卷"]
    
    def get_relationship_types(self) -> List[str]:
        """获取所有关系类型"""
        if not self.graph:
            return ["包含", "关联", "依赖", "属于"]

        try:
            result = self.graph.run("CALL db.relationshipTypes()").data()
            return [record["relationshipType"] for record in result]
        except Exception as e:
            logger.error(f"获取关系类型失败: {e}")
            return ["包含", "关联", "依赖", "属于"]
    
    # 模拟数据方法已移除 - 只使用真实Neo4j数据
    
    # _search_mock_nodes方法已移除 - 只使用真实Neo4j数据
    
    # _get_mock_node_details方法已移除 - 只使用真实Neo4j数据
    
    def close(self):
        """关闭数据库连接"""
        # py2neo的Graph对象不需要显式关闭
        self.graph = None

# 全局Neo4j管理器实例
neo4j_manager = Neo4jManager()
