<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>刷题主页 - 地理题库系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .main-content {
            padding: 2rem 0;
        }
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .stats-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .tag-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
            margin-top: 1rem;
        }
        .tag-item {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            border: 1px solid rgba(102, 126, 234, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        .tag-item:hover {
            background: #667eea;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-geo-alt-fill text-primary me-2"></i>
                地理题库系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('quiz.home') }}">
                            <i class="bi bi-house me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('quiz.quiz_page') }}">
                            <i class="bi bi-pencil-square me-1"></i>开始刷题
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('quiz.profile') }}">
                            <i class="bi bi-person me-1"></i>个人中心
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('quiz.profile') }}">
                                <i class="bi bi-person me-2"></i>个人中心
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container main-content">
        <!-- 欢迎区域 -->
        <div class="row mb-5">
            <div class="col-12 text-center text-white">
                <h1 class="display-4 fw-bold mb-3">欢迎来到地理题库系统</h1>
                <p class="lead">开始您的地理知识学习之旅</p>
            </div>
        </div>

        <!-- 个人统计 -->
        <div class="row mb-5" id="statsSection">
            <div class="col-12">
                <div class="stats-card">
                    <h5 class="mb-3">个人统计</h5>
                    <div class="row" id="statsContent">
                        <div class="col-12 text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能卡片 -->
        <div class="row g-4 mb-5">
            <div class="col-md-6 col-lg-4">
                <div class="feature-card p-4 text-center">
                    <div class="feature-icon">
                        <i class="bi bi-shuffle"></i>
                    </div>
                    <h4 class="mb-3">随机刷题</h4>
                    <p class="text-muted mb-4">随机获取题目，全面提升地理知识水平</p>
                    <a href="{{ url_for('quiz.quiz_page') }}?mode=random" class="btn btn-primary">
                        <i class="bi bi-play-fill me-2"></i>开始刷题
                    </a>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="feature-card p-4 text-center">
                    <div class="feature-icon">
                        <i class="bi bi-tags"></i>
                    </div>
                    <h4 class="mb-3">分类刷题</h4>
                    <p class="text-muted mb-4">按知识点分类练习，针对性提升</p>
                    <button class="btn btn-primary" onclick="showTagModal()">
                        <i class="bi bi-search me-2"></i>选择分类
                    </button>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4">
                <div class="feature-card p-4 text-center">
                    <div class="feature-icon">
                        <i class="bi bi-heart"></i>
                    </div>
                    <h4 class="mb-3">收藏题目</h4>
                    <p class="text-muted mb-4">查看和练习收藏的重点题目</p>
                    <a href="{{ url_for('quiz.profile') }}#favorites" class="btn btn-primary">
                        <i class="bi bi-heart-fill me-2"></i>查看收藏
                    </a>
                </div>
            </div>
        </div>

        <!-- 知识点标签云 -->
        <div class="row">
            <div class="col-12">
                <div class="feature-card p-4">
                    <h5 class="text-center mb-4">知识点分类</h5>
                    <div class="tag-cloud" id="tagCloud">
                        <div class="text-center w-100">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类选择模态框 -->
    <div class="modal fade" id="tagModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">选择知识点分类</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="modalTagList">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadTags();
        });

        // 加载个人统计
        function loadStats() {
            fetch('/quiz/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        document.getElementById('statsContent').innerHTML = `
                            <div class="col-6 col-md-3">
                                <div class="text-primary">
                                    <h3 class="mb-0">${stats.total_count}</h3>
                                    <small>总答题数</small>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="text-success">
                                    <h3 class="mb-0">${stats.correct_count}</h3>
                                    <small>正确题数</small>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="text-info">
                                    <h3 class="mb-0">${stats.accuracy_rate}%</h3>
                                    <small>正确率</small>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="text-warning">
                                    <h3 class="mb-0">${stats.favorite_count}</h3>
                                    <small>收藏题数</small>
                                </div>
                            </div>
                        `;
                    } else {
                        document.getElementById('statsContent').innerHTML = `
                            <div class="col-12 text-center text-muted">
                                <i class="bi bi-exclamation-circle me-2"></i>
                                暂无统计数据
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载统计数据失败:', error);
                    document.getElementById('statsContent').innerHTML = `
                        <div class="col-12 text-center text-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            加载失败
                        </div>
                    `;
                });
        }

        // 加载标签
        function loadTags() {
            fetch('/quiz/api/tags')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.tags.length > 0) {
                        const tagCloud = document.getElementById('tagCloud');
                        const modalTagList = document.getElementById('modalTagList');

                        let tagHtml = '';
                        let modalTagHtml = '<div class="row g-2">';

                        data.tags.forEach(tag => {
                            tagHtml += `<a href="/quiz/quiz?mode=tag&tag=${encodeURIComponent(tag)}" class="tag-item">${tag}</a>`;
                            modalTagHtml += `
                                <div class="col-6 col-md-4">
                                    <a href="/quiz/quiz?mode=tag&tag=${encodeURIComponent(tag)}" class="btn btn-outline-primary btn-sm w-100" data-bs-dismiss="modal">
                                        ${tag}
                                    </a>
                                </div>
                            `;
                        });

                        modalTagHtml += '</div>';

                        tagCloud.innerHTML = tagHtml;
                        modalTagList.innerHTML = modalTagHtml;
                    } else {
                        document.getElementById('tagCloud').innerHTML = `
                            <div class="text-center w-100 text-muted">
                                <i class="bi bi-info-circle me-2"></i>
                                暂无分类标签
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载标签失败:', error);
                    document.getElementById('tagCloud').innerHTML = `
                        <div class="text-center w-100 text-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            加载失败
                        </div>
                    `;
                });
        }

        // 显示标签选择模态框
        function showTagModal() {
            const modal = new bootstrap.Modal(document.getElementById('tagModal'));
            modal.show();
        }
    </script>
</body>
</html>
