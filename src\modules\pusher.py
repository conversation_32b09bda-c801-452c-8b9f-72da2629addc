import os
import json
import logging
import random
import requests
import traceback
from typing import Dict, Any, List, Optional
from datetime import datetime
import pymysql
from jinja2 import FileSystemLoader, Environment
from urllib.parse import urljoin

from config.config import Config

logger = logging.getLogger(__name__)

# 推送配置
def get_pushplus_token():
    """获取PushPlus token"""
    return Config.PUSHER_CONFIG.get('pushplus_token', '')

IMAGE_URL_BASE = Config.PUSHER_CONFIG.get('image_url_base', 'https://47.122.50.189/images/')

def process_images_for_push(image_paths: List[str], image_folder: str) -> List[str]:
    """
    处理图片列表，构建完整的HTTPS图片URL
    """
    processed_images = []

    for image_path in image_paths:
        if not image_path:
            continue

        # 构建完整的图片URL
        if image_path.startswith('http'):
            # 如果已经是完整URL，直接使用
            processed_images.append(image_path)
        else:
            # 构建HTTPS图片URL
            image_url = urljoin(IMAGE_URL_BASE, f"{image_folder}/{image_path}")
            processed_images.append(image_url)

    return processed_images

def get_random_question_by_tag(connection, tag=None) -> dict:
    """
    从数据库中按"知识点标签"随机抽取一道题目。
    """
    try:
        with connection.cursor() as cursor:
            if tag:
                # 如果指定了标签，直接查找包含该标签的题目
                logger.info(f"查找包含标签 '{tag}' 的题目...")
                sql = "SELECT id FROM questions WHERE JSON_CONTAINS(tags, %s)"
                cursor.execute(sql, (json.dumps(tag),))
                question_ids = [row['id'] for row in cursor.fetchall()]

                if not question_ids:
                    logger.info(f"标签 '{tag}' 下没有找到题目。")
                    return None

                random_id = random.choice(question_ids)
                logger.info(f"抽取的题目ID: {random_id}")

                # 获取这道题的完整信息
                return get_question_for_display(random_id, connection)

            else:
                # 如果没有指定标签，随机选择标签
                # 1. 获取所有存在标签的题目
                cursor.execute("SELECT tags FROM questions WHERE JSON_LENGTH(tags) > 0")
                all_tags_records = cursor.fetchall()

                if not all_tags_records:
                    logger.info("数据库中没有找到带标签的题目。")
                    return None

                # 2. 构建一个不重复的标签池
                tag_pool = set()
                for record in all_tags_records:
                    if not record['tags']:
                        continue

                    try:
                        tags = json.loads(record['tags']) if isinstance(record['tags'], str) else record['tags']
                        for tag_item in tags:
                            tag_pool.add(tag_item)
                    except json.JSONDecodeError:
                        logger.warning(f"标签解析失败: {record['tags']}")

                if not tag_pool:
                    logger.info("标签池为空。")
                    return None

                # 3. 随机选择一个标签
                random_tag = random.choice(list(tag_pool))
                logger.info(f"本次随机抽取的知识点: {random_tag}")

                # 4. 随机抽取一道包含该标签的题目ID
                sql = "SELECT id FROM questions WHERE JSON_CONTAINS(tags, %s)"
                cursor.execute(sql, (json.dumps(random_tag),))
                question_ids = [row['id'] for row in cursor.fetchall()]

                if not question_ids:
                    logger.info(f"标签 '{random_tag}' 下没有找到题目。")
                    return None

                random_id = random.choice(question_ids)
                logger.info(f"抽取的题目ID: {random_id}")

                # 5. 获取这道题的完整信息
                return get_question_for_display(random_id, connection)

    except Exception as e:
        logger.error(f"随机抽题时出错: {e}")
        traceback.print_exc()
        return None

def get_question_for_display(question_id: int, connection) -> dict:
    """
    从数据库获取单个题目，并自动合并其所属题组的共享信息和试卷名称。
    """
    try:
        with connection.cursor() as cursor:
            sql = """
            SELECT
                q.*,
                p.image_folder,
                p.paper_name
            FROM
                questions AS q
            LEFT JOIN
                papers AS p ON q.paper_id = p.paper_id
            WHERE
                q.id = %s;
            """
            cursor.execute(sql, (question_id,))
            target_question = cursor.fetchone()

            if not target_question:
                logger.error(f"未找到ID为{question_id}的题目")
                return None

            # 将JSON字符串转换为Python对象
            for field in ['options', 'tags', 'image_paths']:
                if target_question.get(field) and isinstance(target_question[field], str):
                    try:
                        target_question[field] = json.loads(target_question[field])
                    except json.JSONDecodeError:
                        logger.warning(f"题目ID {question_id} 的 {field} 字段JSON解析失败")
                        target_question[field] = []

            # 如果不是题组头，则查找头信息并合并
            if not target_question.get('is_group_header'):
                group_id = target_question.get('group_id')
                if group_id:
                    cursor.execute("SELECT * FROM questions WHERE group_id = %s AND is_group_header = TRUE", (group_id,))
                    header_question = cursor.fetchone()

                    if header_question:
                        # 合并共享材料和共享图片
                        target_question['shared_materials'] = header_question.get('shared_materials')
                        shared_images = header_question.get('image_paths') or []
                        own_images = target_question.get('image_paths') or []

                        # 注意：数据库返回的是JSON字符串，需要解析
                        if isinstance(shared_images, str):
                            try:
                                shared_images = json.loads(shared_images)
                            except json.JSONDecodeError:
                                shared_images = []
                        if isinstance(own_images, str):
                            try:
                                own_images = json.loads(own_images)
                            except json.JSONDecodeError:
                                own_images = []

                        # 移除所有空值并去重
                        all_images = []
                        seen_images = set()
                        for img in shared_images + own_images:
                            if img and img not in seen_images:
                                all_images.append(img)
                                seen_images.add(img)

                        target_question['image_paths'] = all_images

        return target_question
    except Exception as e:
        logger.error(f"获取题目信息时出错: {e}")
        return None

def create_interactive_html(question: dict, use_simple_template: bool = False) -> str:
    """
    将题目数据通过Jinja2模板渲染成HTML
    """
    try:
        # 设置Jinja2环境
        template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'templates')
        env = Environment(loader=FileSystemLoader(template_dir))

        # 根据配置选择模板
        template_name = 'push_simple_template.html' if use_simple_template else 'push_template.html'
        template = env.get_template(template_name)

        # 处理标签数据
        tags = question.get('tags', [])
        if isinstance(tags, str):
            try:
                tags = json.loads(tags)
            except json.JSONDecodeError:
                tags = []
        if not isinstance(tags, list):
            tags = []

        # 处理图片路径，构建HTTPS图片URL
        image_paths = question.get('image_paths', [])
        image_folder = question.get('image_folder', '')
        processed_image_paths = process_images_for_push(image_paths, image_folder)

        # 处理选项数据，支持图片选项
        options = question.get('options', [])
        if isinstance(options, str):
            try:
                options = json.loads(options)
            except json.JSONDecodeError:
                options = []

        # 确保选项格式正确，并处理选项中的图片
        processed_options = []
        if isinstance(options, list):
            for option in options:
                if isinstance(option, dict) and 'key' in option and 'value' in option:
                    # 处理选项中的图片，构建HTTPS URL
                    if 'image_path' in option and option['image_path']:
                        image_url = urljoin(IMAGE_URL_BASE, f"{image_folder}/{option['image_path']}")
                        option['image_url'] = image_url
                    processed_options.append(option)
                elif isinstance(option, str):
                    # 如果是字符串，尝试解析为选项格式
                    processed_options.append({'key': chr(65 + len(processed_options)), 'value': option})

        template_data = {
            'id': question.get('id', ''),
            'question_text': question.get('question_text', ''),
            'shared_materials': question.get('shared_materials', '').replace('\n', '<br>') if question.get('shared_materials') else '',
            'image_paths': processed_image_paths,  # 使用处理后的图片路径
            'image_folder': image_folder,
            'image_url_base': IMAGE_URL_BASE,
            'is_subjective': question.get('is_subjective', False),
            'options': processed_options,
            'correct_answer': question.get('correct_answer', ''),
            'analysis': question.get('analysis', '未提供解析').replace('\n', '<br>') if question.get('analysis') else '未提供解析',
            'paper_info': question.get('paper_name', '未知来源'),
            'tags': tags,
            'current_date': datetime.now().strftime('%Y年%m月%d日')
        }

        return template.render(**template_data)

    except Exception as e:
        logger.error(f"生成HTML时出错: {e}")
        traceback.print_exc()
        return f"<html><body><h1>生成题目时出错</h1><p>{e}</p></body></html>"

def send_pushplus_notification(title: str, content: str) -> bool:
    """发送PushPlus推送通知"""
    token = get_pushplus_token()
    if not token:
        logger.error("PushPlus token未配置")
        return False

    url = 'http://www.pushplus.plus/send'
    data = {
        "token": token,
        "title": title,
        "content": content,
        "template": "html"  # 使用HTML模板
    }
    try:
        response = requests.post(url, json=data, timeout=10)
        result = response.json()
        if result.get('code') == 200:
            logger.info(f"PushPlus推送成功！")
            return True
        else:
            logger.error(f"PushPlus推送失败: {result}")
            return False
    except Exception as e:
        logger.error(f"发送推送时出错: {e}")
        return False

class PusherManager:
    """推送管理器"""
    
    def __init__(self, socketio=None):
        self.socketio = socketio
        self.is_pushing = False
        self.push_history = []
        
    def emit_log(self, message: str, level: str = 'info'):
        """发送日志消息"""
        if self.socketio:
            self.socketio.emit('pusher_log', {
                'message': message,
                'level': level,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        logger.log(getattr(logging, level.upper(), logging.INFO), message)
    
    def get_db_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**Config.DB_CONFIG)
            return connection
        except Exception as e:
            self.emit_log(f"数据库连接失败: {e}", 'error')
            return None
    
    def get_available_tags(self) -> List[str]:
        """获取可用的标签列表"""
        connection = self.get_db_connection()
        if not connection:
            return []
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT DISTINCT tags 
                    FROM questions 
                    WHERE tags IS NOT NULL AND tags != ''
                """)
                results = cursor.fetchall()
                
                # 解析JSON标签
                all_tags = set()
                for row in results:
                    try:
                        tags = json.loads(row['tags'])
                        if isinstance(tags, list):
                            all_tags.update(tags)
                    except (json.JSONDecodeError, TypeError):
                        continue
                
                return sorted(list(all_tags))
                
        except Exception as e:
            self.emit_log(f"获取标签列表失败: {e}", 'error')
            return []
        finally:
            connection.close()
    
    def get_question_stats(self) -> Dict[str, Any]:
        """获取题目统计信息"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        
        try:
            with connection.cursor() as cursor:
                # 总题目数
                cursor.execute("SELECT COUNT(*) as total FROM questions")
                total = cursor.fetchone()['total']
                
                # 选择题数量
                cursor.execute("SELECT COUNT(*) as count FROM questions WHERE is_subjective = 0")
                choice_count = cursor.fetchone()['count']
                
                # 主观题数量
                cursor.execute("SELECT COUNT(*) as count FROM questions WHERE is_subjective = 1")
                subjective_count = cursor.fetchone()['count']
                
                # 按试卷分组统计
                cursor.execute("""
                    SELECT p.paper_name, COUNT(q.id) as question_count
                    FROM papers p
                    LEFT JOIN questions q ON p.paper_id = q.paper_id
                    GROUP BY p.paper_id, p.paper_name
                    ORDER BY question_count DESC
                """)
                paper_stats = cursor.fetchall()
                
                return {
                    'total_questions': total,
                    'choice_questions': choice_count,
                    'subjective_questions': subjective_count,
                    'paper_stats': paper_stats
                }
                
        except Exception as e:
            self.emit_log(f"获取题目统计失败: {e}", 'error')
            return {}
        finally:
            connection.close()
    
    def preview_random_question(self, tag: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """预览随机题目"""
        connection = self.get_db_connection()
        if not connection:
            self.emit_log("数据库连接失败", 'error')
            return None

        try:
            self.emit_log(f"开始获取随机题目，标签: {tag if tag else '随机'}", 'info')
            question = get_random_question_by_tag(connection, tag)
            if question:
                self.emit_log(f"预览题目: {question.get('id', '未知')} - {question.get('question_text', '')[:50]}...", 'info')
                return question
            else:
                self.emit_log("没有找到符合条件的题目", 'warning')
                return None

        except Exception as e:
            self.emit_log(f"预览题目失败: {e}", 'error')
            logger.exception("预览题目详细错误信息:")
            return None
        finally:
            connection.close()
    
    def generate_html_preview(self, question: Dict[str, Any], use_simple_template: bool = False) -> str:
        """生成HTML预览"""
        try:
            html_content = create_interactive_html(question, use_simple_template)
            template_type = "简化版" if use_simple_template else "完整版"
            self.emit_log(f"HTML预览生成成功 ({template_type})", 'info')
            return html_content
        except Exception as e:
            self.emit_log(f"生成HTML预览失败: {e}", 'error')
            return f"<html><body><h1>预览生成失败</h1><p>{e}</p></body></html>"
    
    def send_push_notification(self, question: Dict[str, Any], custom_title: Optional[str] = None) -> bool:
        """发送推送通知"""
        if self.is_pushing:
            self.emit_log("推送正在进行中，请等待完成", 'warning')
            return False

        try:
            self.is_pushing = True
            self.emit_log("开始发送推送通知", 'info')

            # 根据配置选择模板类型
            push_mode = Config.PUSHER_CONFIG.get('push_mode', 'simple')
            use_simple_template = (push_mode == 'simple')

            # 生成HTML内容
            html_content = self.generate_html_preview(question, use_simple_template)

            # 生成标题
            if custom_title:
                title = custom_title
            else:
                title = f"每日地理一题 [第{question.get('id', '未知')}题]"

            # 发送推送
            send_pushplus_notification(title, html_content)
            
            # 记录推送历史
            push_record = {
                'timestamp': datetime.now(),
                'question_id': question.get('id'),
                'title': title,
                'question_text': question.get('question_text', '')[:100] + '...',
                'status': 'success'
            }
            self.push_history.append(push_record)
            
            # 保持历史记录不超过100条
            if len(self.push_history) > 100:
                self.push_history = self.push_history[-100:]
            
            self.emit_log(f"推送发送成功: {title}", 'info')
            
            if self.socketio:
                self.socketio.emit('pusher_complete', {
                    'question_id': question.get('id'),
                    'title': title,
                    'timestamp': push_record['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                })
            
            return True
            
        except Exception as e:
            self.emit_log(f"发送推送失败: {e}", 'error')
            return False
        finally:
            self.is_pushing = False
    
    def get_push_history(self) -> List[Dict[str, Any]]:
        """获取推送历史"""
        # 转换datetime对象为字符串以便JSON序列化
        history = []
        for record in self.push_history:
            history_item = record.copy()
            history_item['timestamp'] = record['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            history.append(history_item)
        
        return history
    
    def test_push_service(self) -> bool:
        """测试推送服务"""
        try:
            self.emit_log("测试推送服务连接", 'info')
            
            test_content = """
            <html>
            <body style="font-family: Arial, sans-serif; padding: 20px;">
                <h2>🧪 推送服务测试</h2>
                <p>这是一条测试消息，用于验证推送服务是否正常工作。</p>
                <p><strong>测试时间:</strong> {}</p>
                <p><strong>系统状态:</strong> 正常运行</p>
            </body>
            </html>
            """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            send_pushplus_notification("地理题库系统 - 推送测试", test_content)
            
            self.emit_log("测试推送发送成功", 'info')
            return True
            
        except Exception as e:
            self.emit_log(f"测试推送失败: {e}", 'error')
            return False
    
    def get_push_config(self) -> Dict[str, Any]:
        """获取推送配置"""
        return {
            'pushplus_token': Config.PUSHER_CONFIG['pushplus_token'][:10] + '...' if Config.PUSHER_CONFIG['pushplus_token'] else '未配置',
            'image_url_base': Config.PUSHER_CONFIG['image_url_base'],
            'template_file': Config.PUSHER_CONFIG['template_file']
        }
    
    def update_push_config(self, new_config: Dict[str, Any]) -> bool:
        """更新推送配置"""
        try:
            # 更新内存中的配置
            if 'pushplus_token' in new_config:
                Config.PUSHER_CONFIG['pushplus_token'] = new_config['pushplus_token']
            if 'image_url_base' in new_config:
                Config.PUSHER_CONFIG['image_url_base'] = new_config['image_url_base']

            # 这里可以添加保存到配置文件的逻辑
            # 目前先保存到内存中

            self.emit_log("推送配置更新成功", 'info')
            return True
        except Exception as e:
            self.emit_log(f"更新推送配置失败: {e}", 'error')
            return False
    
    def get_push_status(self) -> Dict[str, Any]:
        """获取推送状态"""
        return {
            'is_pushing': self.is_pushing,
            'history_count': len(self.push_history),
            'last_push': self.push_history[-1]['timestamp'].strftime('%Y-%m-%d %H:%M:%S') if self.push_history else None
        }
