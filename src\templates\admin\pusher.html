{% extends "base.html" %}

{% block title %}推送管理 - 地理题库管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-paper-plane"></i> 推送管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-success" onclick="sendPush()">
            <i class="fas fa-paper-plane"></i> 发送推送
        </button>
        <button type="button" class="btn btn-outline-primary ms-2" onclick="testPushService()">
            <i class="fas fa-vial"></i> 测试服务
        </button>
        <button type="button" class="btn btn-outline-secondary ms-2" onclick="showConfigModal()">
            <i class="fas fa-cog"></i> 推送配置
        </button>
    </div>
</div>

<!-- 题目统计卡片 -->
<div class="row mb-4" id="stats-cards">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number" id="total-questions">--</div>
            <div class="stats-label">
                <i class="fas fa-question-circle"></i> 题目总数
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="stats-number" id="choice-questions">--</div>
            <div class="stats-label">
                <i class="fas fa-check-circle"></i> 选择题
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="stats-number" id="subjective-questions">--</div>
            <div class="stats-label">
                <i class="fas fa-edit"></i> 主观题
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="stats-number" id="push-count">0</div>
            <div class="stats-label">
                <i class="fas fa-history"></i> 推送次数
            </div>
        </div>
    </div>
</div>

<!-- 推送配置 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-cog"></i> 推送配置
            </div>
            <div class="card-body">
                <!-- 选择模式 -->
                <div class="mb-3">
                    <label class="form-label">选择模式</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="selectionMode" id="tagMode" value="tag" checked onchange="toggleSelectionMode()">
                        <label class="form-check-label" for="tagMode">
                            <i class="fas fa-tags"></i> 按标签选择
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="selectionMode" id="manualMode" value="manual" onchange="toggleSelectionMode()">
                        <label class="form-check-label" for="manualMode">
                            <i class="fas fa-hand-pointer"></i> 手动选择题目
                        </label>
                    </div>
                </div>

                <!-- 标签选择模式 -->
                <div id="tagSelectionGroup" class="mb-3">
                    <label for="tagSelect" class="form-label">选择标签 (可选)</label>
                    <select class="form-select" id="tagSelect">
                        <option value="">-- 随机选择 --</option>
                    </select>
                    <div class="form-text">选择特定标签的题目，留空则随机选择</div>
                </div>

                <!-- 手动选择模式 -->
                <div id="manualSelectionGroup" class="mb-3" style="display: none;">
                    <div class="mb-3">
                        <label for="paperSelect" class="form-label">选择试卷</label>
                        <select class="form-select" id="paperSelect" onchange="loadQuestionsFromPaper()">
                            <option value="">-- 请选择试卷 --</option>
                        </select>
                        <div class="form-text">先选择试卷，然后选择具体题目</div>
                    </div>
                    <div class="mb-3">
                        <label for="questionSelect" class="form-label">选择题目</label>
                        <select class="form-select" id="questionSelect" disabled>
                            <option value="">-- 请先选择试卷 --</option>
                        </select>
                        <div class="form-text">从选定试卷中选择具体题目</div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="customTitle" class="form-label">自定义标题 (可选)</label>
                    <input type="text" class="form-control" id="customTitle" placeholder="留空使用默认标题">
                    <div class="form-text">自定义推送标题，留空则使用默认格式</div>
                </div>
                <button type="button" class="btn btn-primary" onclick="previewQuestion()">
                    <i class="fas fa-eye"></i> 预览题目
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> 推送状态
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-6">
                        <strong>服务状态:</strong>
                    </div>
                    <div class="col-sm-6">
                        <span class="badge bg-success" id="service-status">正常</span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-sm-6">
                        <strong>最后推送:</strong>
                    </div>
                    <div class="col-sm-6">
                        <span id="last-push-time">暂无记录</span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-sm-6">
                        <strong>推送历史:</strong>
                    </div>
                    <div class="col-sm-6">
                        <span id="history-count">0</span> 条记录
                    </div>
                </div>
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="refreshStats()">
                        <i class="fas fa-sync-alt"></i> 刷新状态
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 题目预览 -->
<div class="card mb-4" id="preview-card" style="display: none;">
    <div class="card-header">
        <i class="fas fa-eye"></i> 题目预览
        <button class="btn btn-sm btn-outline-secondary float-end" onclick="hidePreview()">
            <i class="fas fa-times"></i> 关闭
        </button>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>题目信息</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>题目ID:</strong></td>
                        <td id="preview-id">--</td>
                    </tr>
                    <tr>
                        <td><strong>题目类型:</strong></td>
                        <td id="preview-type">--</td>
                    </tr>
                    <tr>
                        <td><strong>来源:</strong></td>
                        <td id="preview-source">--</td>
                    </tr>
                    <tr>
                        <td><strong>标签:</strong></td>
                        <td id="preview-tags">--</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>推送预览</h6>
                <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                    <div id="html-preview">
                        <!-- HTML预览内容 -->
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-3">
            <button type="button" class="btn btn-success" onclick="confirmSendPush()">
                <i class="fas fa-paper-plane"></i> 确认发送此题目
            </button>
            <button type="button" class="btn btn-outline-primary ms-2" onclick="previewQuestion()">
                <i class="fas fa-dice"></i> 重新随机选择
            </button>
        </div>
    </div>
</div>

<!-- 推送历史 -->
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-history"></i> 推送历史
        <button class="btn btn-sm btn-outline-primary float-end" onclick="refreshHistory()">
            <i class="fas fa-sync-alt"></i> 刷新
        </button>
    </div>
    <div class="card-body">
        <div id="history-container">
            <div class="text-center text-muted py-4">
                <i class="fas fa-clock fa-3x mb-3"></i>
                <p>暂无推送历史</p>
            </div>
        </div>
    </div>
</div>

<!-- 推送日志 -->
<div class="card">
    <div class="card-header">
        <i class="fas fa-terminal"></i> 推送日志
        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearPushLogs()">
            <i class="fas fa-trash"></i> 清空
        </button>
    </div>
    <div class="card-body">
        <div id="push-log-container" class="log-container">
            <div class="log-entry log-info">
                <span class="log-timestamp">[系统启动]</span>
                推送管理模块已就绪，等待操作...
            </div>
        </div>
    </div>
</div>

<!-- 推送配置模态框 -->
<div class="modal fade" id="configModal" tabindex="-1" aria-labelledby="configModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="configModalLabel">
                    <i class="fas fa-cog"></i> 推送配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="configForm">
                    <div class="mb-3">
                        <label for="pushplusToken" class="form-label">
                            <i class="fas fa-key"></i> PushPlus Token
                        </label>
                        <input type="text" class="form-control" id="pushplusToken"
                               placeholder="请输入您的PushPlus Token">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            请到 <a href="http://www.pushplus.plus" target="_blank">PushPlus官网</a> 获取您的Token
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="imageUrlBase" class="form-label">
                            <i class="fas fa-image"></i> 图片服务器地址
                        </label>
                        <input type="text" class="form-control" id="imageUrlBase"
                               placeholder="http://47.122.50.189/images/">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            题目图片的服务器基础地址
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveConfig()">
                    <i class="fas fa-save"></i> 保存配置
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
let currentQuestion = null;
let availableTags = [];

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadTags();
    loadPapers();
    refreshStats();
    refreshHistory();
});

function loadTags() {
    fetch('/api/pusher/tags')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                availableTags = data.tags;
                const select = document.getElementById('tagSelect');
                select.innerHTML = '<option value="">-- 随机选择 --</option>';
                
                data.tags.forEach(tag => {
                    const option = document.createElement('option');
                    option.value = tag;
                    option.textContent = tag;
                    select.appendChild(option);
                });
                
                addPushLog(`加载了 ${data.tags.length} 个标签`, 'info');
            } else {
                addPushLog(`加载标签失败: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            addPushLog(`加载标签请求失败: ${error}`, 'error');
        });
}

function loadPapers() {
    fetch('/api/papers')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('paperSelect');
                select.innerHTML = '<option value="">-- 请选择试卷 --</option>';

                data.papers.forEach(paper => {
                    const option = document.createElement('option');
                    option.value = paper.id;
                    option.textContent = `${paper.title} (${paper.question_count}题)`;
                    select.appendChild(option);
                });

                addPushLog(`加载了 ${data.papers.length} 份试卷`, 'info');
            } else {
                addPushLog(`加载试卷失败: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            addPushLog(`加载试卷请求失败: ${error}`, 'error');
        });
}

function toggleSelectionMode() {
    const tagMode = document.getElementById('tagMode').checked;
    const tagGroup = document.getElementById('tagSelectionGroup');
    const manualGroup = document.getElementById('manualSelectionGroup');

    if (tagMode) {
        tagGroup.style.display = 'block';
        manualGroup.style.display = 'none';
        // 清空手动选择的内容
        document.getElementById('paperSelect').value = '';
        document.getElementById('questionSelect').value = '';
        document.getElementById('questionSelect').disabled = true;
    } else {
        tagGroup.style.display = 'none';
        manualGroup.style.display = 'block';
        // 清空标签选择
        document.getElementById('tagSelect').value = '';
    }
}

function loadQuestionsFromPaper() {
    const paperId = document.getElementById('paperSelect').value;
    const questionSelect = document.getElementById('questionSelect');

    if (!paperId) {
        questionSelect.innerHTML = '<option value="">-- 请先选择试卷 --</option>';
        questionSelect.disabled = true;
        return;
    }

    questionSelect.innerHTML = '<option value="">-- 加载中... --</option>';
    questionSelect.disabled = true;

    fetch(`/api/papers/${paperId}/questions`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                questionSelect.innerHTML = '<option value="">-- 请选择题目 --</option>';

                data.questions.forEach((question, index) => {
                    const option = document.createElement('option');
                    option.value = question.id;
                    // 截取题目内容的前50个字符作为显示
                    const shortContent = question.content.length > 50
                        ? question.content.substring(0, 50) + '...'
                        : question.content;
                    option.textContent = `第${index + 1}题: ${shortContent}`;
                    questionSelect.appendChild(option);
                });

                questionSelect.disabled = false;
                addPushLog(`加载了试卷中的 ${data.questions.length} 道题目`, 'info');
            } else {
                questionSelect.innerHTML = '<option value="">-- 加载失败 --</option>';
                addPushLog(`加载题目失败: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            questionSelect.innerHTML = '<option value="">-- 加载失败 --</option>';
            addPushLog(`加载题目请求失败: ${error}`, 'error');
        });
}

function refreshStats() {
    fetch('/api/pusher/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.stats;
                document.getElementById('total-questions').textContent = stats.total_questions || '--';
                document.getElementById('choice-questions').textContent = stats.choice_questions || '--';
                document.getElementById('subjective-questions').textContent = stats.subjective_questions || '--';
            }
        })
        .catch(error => {
            console.error('获取统计数据失败:', error);
        });
}

function previewQuestion() {
    const isManualMode = document.getElementById('manualMode').checked;

    if (isManualMode) {
        // 手动选择模式
        const paperId = document.getElementById('paperSelect').value;
        const questionId = document.getElementById('questionSelect').value;

        if (!paperId) {
            addPushLog('请先选择试卷', 'error');
            return;
        }

        if (!questionId) {
            addPushLog('请先选择题目', 'error');
            return;
        }

        addPushLog('正在获取指定题目...', 'info');

        // 修改：添加试卷ID验证，确保题目属于选定的试卷
        fetch(`/api/questions/${questionId}?paper_id=${paperId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentQuestion = data.question;
                    showPreview(data.question);
                    addPushLog(`获取题目成功: ID ${data.question.id}`, 'info');
                } else {
                    addPushLog(`获取题目失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                addPushLog(`请求失败: ${error}`, 'error');
            });
    } else {
        // 标签选择模式
        const tag = document.getElementById('tagSelect').value || null;

        addPushLog('正在获取随机题目...', 'info');

        fetch('/api/pusher/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({tag: tag})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentQuestion = data.question;
                showPreview(data.question);
                addPushLog(`获取题目成功: ID ${data.question.id}`, 'info');
            } else {
                addPushLog(`获取题目失败: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            addPushLog(`请求失败: ${error}`, 'error');
        });
    }
}

function showPreview(question) {
    document.getElementById('preview-card').style.display = 'block';
    
    // 填充题目信息
    document.getElementById('preview-id').textContent = question.id || '--';
    document.getElementById('preview-type').textContent = question.is_subjective ? '主观题' : '选择题';
    document.getElementById('preview-source').textContent = question.paper_name || question.source || '--';
    
    // 处理标签
    let tagsText = '--';
    if (question.tags) {
        try {
            const tags = typeof question.tags === 'string' ? JSON.parse(question.tags) : question.tags;
            tagsText = Array.isArray(tags) ? tags.join(', ') : tags;
        } catch (e) {
            tagsText = question.tags;
        }
    }
    document.getElementById('preview-tags').textContent = tagsText;
    
    // 生成HTML预览（简化版）
    const previewContainer = document.getElementById('html-preview');
    // 兼容两种数据结构：question_text（标签模式）和 content（手动模式）
    const questionContent = question.question_text || question.content || '无题目内容';
    let previewHtml = `
        <div style="font-family: Arial, sans-serif;">
            <h5>题目 ${question.id}</h5>
            <p><strong>题目:</strong> ${questionContent}</p>
    `;
    
    if (question.options && Array.isArray(question.options)) {
        previewHtml += '<p><strong>选项:</strong></p><ul>';
        question.options.forEach(opt => {
            previewHtml += `<li>${opt.key}. ${opt.value}</li>`;
        });
        previewHtml += '</ul>';
    }
    
    previewHtml += `<p><strong>答案:</strong> ${question.correct_answer || '无答案'}</p>`;
    previewHtml += '</div>';
    
    previewContainer.innerHTML = previewHtml;
}

function hidePreview() {
    document.getElementById('preview-card').style.display = 'none';
    currentQuestion = null;
}

function confirmSendPush() {
    if (!currentQuestion) {
        alert('没有选择题目');
        return;
    }
    
    sendPushWithQuestion(currentQuestion);
}

function sendPush() {
    if (currentQuestion) {
        sendPushWithQuestion(currentQuestion);
    } else {
        // 先获取题目再发送
        previewQuestion();
        setTimeout(() => {
            if (currentQuestion) {
                sendPushWithQuestion(currentQuestion);
            }
        }, 1000);
    }
}

function sendPushWithQuestion(question) {
    const customTitle = document.getElementById('customTitle').value || null;
    
    addPushLog('开始发送推送...', 'info');
    
    fetch('/api/pusher/send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            question: question,
            title: customTitle
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addPushLog('推送发送成功！', 'info');
            hidePreview();
            refreshHistory();
            document.getElementById('customTitle').value = '';
        } else {
            addPushLog(`推送发送失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        addPushLog(`发送请求失败: ${error}`, 'error');
    });
}

function testPushService() {
    addPushLog('正在测试推送服务...', 'info');
    
    fetch('/api/pusher/test')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addPushLog('推送服务测试成功！', 'info');
            } else {
                addPushLog(`推送服务测试失败: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            addPushLog(`测试请求失败: ${error}`, 'error');
        });
}

function refreshHistory() {
    fetch('/api/pusher/history')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayHistory(data.history);
                document.getElementById('push-count').textContent = data.history.length;
                document.getElementById('history-count').textContent = data.history.length;
                
                if (data.history.length > 0) {
                    document.getElementById('last-push-time').textContent = data.history[data.history.length - 1].timestamp;
                }
            } else {
                addPushLog(`获取推送历史失败: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            addPushLog(`获取历史请求失败: ${error}`, 'error');
        });
}

function displayHistory(history) {
    const container = document.getElementById('history-container');
    
    if (history.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-clock fa-3x mb-3"></i>
                <p>暂无推送历史</p>
            </div>
        `;
        return;
    }
    
    let historyHtml = '<div class="list-group">';
    history.reverse().forEach((record, index) => {
        if (index < 10) { // 只显示最近10条
            historyHtml += `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${record.title}</h6>
                        <small>${record.timestamp}</small>
                    </div>
                    <p class="mb-1">${record.question_text}</p>
                    <small>题目ID: ${record.question_id}</small>
                </div>
            `;
        }
    });
    historyHtml += '</div>';
    
    container.innerHTML = historyHtml;
}

function addPushLog(message, level) {
    const logContainer = document.getElementById('push-log-container');
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${level}`;
    logEntry.innerHTML = `<span class="log-timestamp">[${new Date().toLocaleString()}]</span>${message}`;
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

function clearPushLogs() {
    const logContainer = document.getElementById('push-log-container');
    logContainer.innerHTML = '<div class="log-entry log-info"><span class="log-timestamp">[' + 
        new Date().toLocaleString() + ']</span>日志已清空</div>';
}

// 监听Socket.IO事件
socket.on('pusher_log', function(data) {
    addPushLog(data.message, data.level);
});

socket.on('pusher_complete', function(data) {
    addPushLog(`推送完成: ${data.title}`, 'info');
    refreshHistory();
});

// 配置相关函数
function showConfigModal() {
    // 加载当前配置
    fetch('/api/pusher/config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const config = data.config;
                // 填充表单
                document.getElementById('pushplusToken').value = config.pushplus_token === '未配置' ? '' : config.pushplus_token.replace('...', '');
                document.getElementById('imageUrlBase').value = config.image_url_base || '';
            }
        })
        .catch(error => {
            console.error('加载配置失败:', error);
            addPushLog('加载配置失败', 'error');
        });

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('configModal'));
    modal.show();
}

function saveConfig() {
    const token = document.getElementById('pushplusToken').value.trim();
    const imageUrlBase = document.getElementById('imageUrlBase').value.trim();

    if (!token) {
        alert('请输入PushPlus Token');
        return;
    }

    const config = {
        pushplus_token: token,
        image_url_base: imageUrlBase || 'http://47.122.50.189/images/'
    };

    // 发送配置更新请求
    fetch('/api/pusher/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addPushLog('配置保存成功', 'info');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('configModal'));
            modal.hide();
        } else {
            addPushLog(`配置保存失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        console.error('保存配置失败:', error);
        addPushLog('保存配置失败', 'error');
    });
}
</script>
{% endblock %}
