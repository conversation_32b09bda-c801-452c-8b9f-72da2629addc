{% extends "base.html" %}

{% block title %}数据库上传 - 地理题库管理系统{% endblock %}

{% block extra_css %}
<style>
.chart-container {
    position: relative;
    height: 200px !important;
    width: 100% !important;
    overflow: hidden;
}

.chart-container canvas {
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
}

/* 防止图表容器高度变化 */
#questionTypeChart {
    max-width: 100%;
    max-height: 200px;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-database"></i> 数据库管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-success" onclick="startUpload()">
            <i class="fas fa-upload"></i> 开始上传
        </button>
    </div>
</div>

<!-- 数据库统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">
                {% if stats %}{{ stats.papers_count }}{% else %}--{% endif %}
            </div>
            <div class="stats-label">
                <i class="fas fa-file-alt"></i> 试卷总数
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="stats-number">
                {% if stats %}{{ stats.questions_count }}{% else %}--{% endif %}
            </div>
            <div class="stats-label">
                <i class="fas fa-question-circle"></i> 题目总数
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="stats-number" id="pending-files">
                0
            </div>
            <div class="stats-label">
                <i class="fas fa-clock"></i> 待上传文件
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="stats-number" id="upload-status">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-label">
                <i class="fas fa-cloud-upload-alt"></i> 上传状态
            </div>
        </div>
    </div>
</div>

<!-- JSON文件选择 -->
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-file-code"></i> JSON文件管理
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="jsonFile" class="form-label">选择JSON文件</label>
                    <input class="form-control" type="file" id="jsonFile" accept=".json" multiple>
                    <div class="form-text">选择预处理程序生成的JSON文件</div>
                </div>
                <button type="button" class="btn btn-primary" onclick="loadJsonFiles()">
                    <i class="fas fa-folder-open"></i> 加载文件
                </button>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">或选择已处理的文件</label>
                    <select class="form-select" id="processedFiles" onchange="selectProcessedFile()">
                        <option value="">-- 选择文件 --</option>
                    </select>
                    <div class="form-text">从预处理程序输出目录选择</div>
                </div>
                <button type="button" class="btn btn-outline-primary" onclick="refreshProcessedFiles()">
                    <i class="fas fa-sync-alt"></i> 刷新列表
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 文件预览 -->
<div class="card mb-4" id="preview-card" style="display: none;">
    <div class="card-header">
        <i class="fas fa-eye"></i> 文件预览
        <span class="badge bg-info ms-2" id="preview-filename"></span>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>试卷名称:</strong></td>
                        <td id="preview-paper-name">--</td>
                    </tr>
                    <tr>
                        <td><strong>题目数量:</strong></td>
                        <td id="preview-question-count">--</td>
                    </tr>
                    <tr>
                        <td><strong>选择题:</strong></td>
                        <td id="preview-choice-count">--</td>
                    </tr>
                    <tr>
                        <td><strong>主观题:</strong></td>
                        <td id="preview-subjective-count">--</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>题目类型分布</h6>
                <div class="chart-container" style="position: relative; height: 200px; width: 100%;">
                    <canvas id="questionTypeChart"></canvas>
                </div>
            </div>
        </div>
        <div class="mt-3">
            <h6>题目预览 (前3题)</h6>
            <div id="question-preview" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                <!-- 题目预览内容 -->
            </div>
        </div>
    </div>
</div>

<!-- 上传进度 -->
<div class="card mb-4" id="upload-progress-card" style="display: none;">
    <div class="card-header">
        <i class="fas fa-cloud-upload-alt"></i> 上传进度
    </div>
    <div class="card-body">
        <div class="progress mb-3">
            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                 role="progressbar" 
                 style="width: 0%" 
                 id="upload-progress-bar">0%</div>
        </div>
        <div id="upload-current-task" class="text-muted">等待开始...</div>
        <div class="mt-2">
            <small class="text-muted">
                <span id="upload-processed">0</span> / <span id="upload-total">0</span> 题目已处理
            </small>
        </div>
    </div>
</div>

<!-- 上传日志 -->
<div class="card">
    <div class="card-header">
        <i class="fas fa-terminal"></i> 上传日志
        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearUploadLogs()">
            <i class="fas fa-trash"></i> 清空
        </button>
    </div>
    <div class="card-body">
        <div id="upload-log-container" class="log-container">
            <div class="log-entry log-info">
                <span class="log-timestamp">[系统启动]</span>
                数据库上传模块已就绪，等待操作...
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let currentJsonData = null;
let uploadInProgress = false;
let questionTypeChart = null;

function loadJsonFiles() {
    const fileInput = document.getElementById('jsonFile');
    const files = fileInput.files;
    
    if (files.length === 0) {
        alert('请选择JSON文件');
        return;
    }
    
    // 读取第一个文件
    const file = files[0];
    const reader = new FileReader();
    
    reader.onload = function(e) {
        try {
            const jsonData = JSON.parse(e.target.result);
            currentJsonData = jsonData;
            previewJsonData(jsonData, file.name);
            addUploadLog(`已加载文件: ${file.name}`, 'info');
        } catch (error) {
            addUploadLog(`JSON解析失败: ${error.message}`, 'error');
        }
    };
    
    reader.readAsText(file);
}

function selectProcessedFile() {
    const select = document.getElementById('processedFiles');
    const filename = select.value;
    
    if (!filename) return;
    
    // 从服务器加载文件
    fetch(`/api/uploader/load-processed-file?filename=${encodeURIComponent(filename)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentJsonData = data.data;
                previewJsonData(data.data, filename);
                addUploadLog(`已加载处理文件: ${filename}`, 'info');
            } else {
                addUploadLog(`加载失败: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            addUploadLog(`请求失败: ${error}`, 'error');
        });
}

function previewJsonData(jsonData, filename) {
    if (!Array.isArray(jsonData) || jsonData.length === 0) {
        addUploadLog('JSON数据格式不正确或为空', 'error');
        return;
    }
    
    // 显示预览卡片
    document.getElementById('preview-card').style.display = 'block';
    document.getElementById('preview-filename').textContent = filename;
    
    // 基本信息
    const paperName = jsonData[0].source || '未知试卷';
    const totalQuestions = jsonData.length;
    const choiceQuestions = jsonData.filter(q => !q.is_subjective).length;
    const subjectiveQuestions = jsonData.filter(q => q.is_subjective).length;
    
    document.getElementById('preview-paper-name').textContent = paperName;
    document.getElementById('preview-question-count').textContent = totalQuestions;
    document.getElementById('preview-choice-count').textContent = choiceQuestions;
    document.getElementById('preview-subjective-count').textContent = subjectiveQuestions;
    
    // 更新待上传文件数
    document.getElementById('pending-files').textContent = totalQuestions;
    
    // 绘制图表
    drawQuestionTypeChart(choiceQuestions, subjectiveQuestions);
    
    // 题目预览
    const previewContainer = document.getElementById('question-preview');
    previewContainer.innerHTML = '';
    
    const previewQuestions = jsonData.slice(0, 3);
    previewQuestions.forEach((question, index) => {
        const questionDiv = document.createElement('div');
        questionDiv.className = 'mb-3 p-2 border-start border-primary border-3';
        questionDiv.innerHTML = `
            <h6>题目 ${question.id}: ${question.is_subjective ? '主观题' : '选择题'}</h6>
            <p class="mb-1"><strong>题目:</strong> ${question.question_text || '无题目内容'}</p>
            ${question.options ? `<p class="mb-1"><strong>选项:</strong> ${question.options.map(opt => opt.key + '. ' + opt.value).join('; ')}</p>` : ''}
            <p class="mb-0"><strong>答案:</strong> ${question.correct_answer || '无答案'}</p>
        `;
        previewContainer.appendChild(questionDiv);
    });
}

function drawQuestionTypeChart(choiceCount, subjectiveCount) {
    try {
        const canvas = document.getElementById('questionTypeChart');
        if (!canvas) {
            console.error('找不到图表canvas元素');
            return;
        }

        const ctx = canvas.getContext('2d');

        // 销毁现有图表
        if (questionTypeChart) {
            questionTypeChart.destroy();
            questionTypeChart = null;
        }

        // 重置canvas尺寸
        const container = canvas.parentElement;
        canvas.width = container.clientWidth;
        canvas.height = container.clientHeight;

        // 如果没有数据，显示提示信息
        if (choiceCount === 0 && subjectiveCount === 0) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#6c757d';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('暂无数据', canvas.width / 2, canvas.height / 2);
            return;
        }

        // 创建新图表
        questionTypeChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['选择题', '主观题'],
                datasets: [{
                    data: [choiceCount, subjectiveCount],
                    backgroundColor: ['#36a2eb', '#ff6384'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: false, // 关闭响应式，使用固定尺寸
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 8,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = choiceCount + subjectiveCount;
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: false,
                    duration: 800
                },
                layout: {
                    padding: {
                        top: 10,
                        bottom: 10,
                        left: 10,
                        right: 10
                    }
                }
            }
        });

        console.log('图表绘制完成:', { choiceCount, subjectiveCount });

    } catch (error) {
        console.error('绘制图表时发生错误:', error);
        // 显示错误信息
        const canvas = document.getElementById('questionTypeChart');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#dc3545';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('图表加载失败', canvas.width / 2, canvas.height / 2);
        }
    }
}

function startUpload() {
    if (!currentJsonData) {
        alert('请先选择并加载JSON文件');
        return;
    }
    
    if (uploadInProgress) {
        alert('上传正在进行中，请等待完成');
        return;
    }
    
    uploadInProgress = true;
    document.getElementById('upload-progress-card').style.display = 'block';
    
    addUploadLog('开始上传数据到数据库...', 'info');
    updateUploadProgress(0, '准备上传数据...', 0, currentJsonData.length);
    
    // 发送上传请求
    fetch('/api/uploader/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            data: currentJsonData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addUploadLog('上传任务已启动', 'info');
        } else {
            addUploadLog(`启动失败: ${data.message}`, 'error');
            uploadInProgress = false;
            document.getElementById('upload-progress-card').style.display = 'none';
        }
    })
    .catch(error => {
        addUploadLog(`请求失败: ${error}`, 'error');
        uploadInProgress = false;
        document.getElementById('upload-progress-card').style.display = 'none';
    });
}

function updateUploadProgress(percentage, task, processed, total) {
    const progressBar = document.getElementById('upload-progress-bar');
    const currentTask = document.getElementById('upload-current-task');
    const processedSpan = document.getElementById('upload-processed');
    const totalSpan = document.getElementById('upload-total');
    
    progressBar.style.width = percentage + '%';
    progressBar.textContent = Math.round(percentage) + '%';
    currentTask.textContent = task;
    processedSpan.textContent = processed;
    totalSpan.textContent = total;
    
    if (percentage >= 100) {
        progressBar.classList.remove('progress-bar-animated');
        uploadInProgress = false;
        setTimeout(() => {
            document.getElementById('upload-progress-card').style.display = 'none';
        }, 3000);
    }
}

function refreshProcessedFiles() {
    fetch('/api/uploader/processed-files')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('processedFiles');
            select.innerHTML = '<option value="">-- 选择文件 --</option>';
            
            if (data.success && data.files) {
                data.files.forEach(file => {
                    const option = document.createElement('option');
                    option.value = file;
                    option.textContent = file;
                    select.appendChild(option);
                });
                addUploadLog(`刷新文件列表完成，找到 ${data.files.length} 个文件`, 'info');
            } else {
                addUploadLog('刷新文件列表失败', 'error');
            }
        })
        .catch(error => {
            addUploadLog(`刷新请求失败: ${error}`, 'error');
        });
}

function addUploadLog(message, level) {
    const logContainer = document.getElementById('upload-log-container');
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${level}`;
    logEntry.innerHTML = `<span class="log-timestamp">[${new Date().toLocaleString()}]</span>${message}`;
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

function clearUploadLogs() {
    const logContainer = document.getElementById('upload-log-container');
    logContainer.innerHTML = '<div class="log-entry log-info"><span class="log-timestamp">[' + 
        new Date().toLocaleString() + ']</span>日志已清空</div>';
}

// 监听Socket.IO事件
socket.on('uploader_progress', function(data) {
    updateUploadProgress(data.percentage, data.task, data.processed, data.total);
});

socket.on('uploader_log', function(data) {
    addUploadLog(data.message, data.level);
});

socket.on('uploader_complete', function(data) {
    addUploadLog('数据库上传完成！', 'info');
    updateUploadProgress(100, '上传完成', data.processed, data.total);
    
    // 刷新统计数据
    setTimeout(() => {
        location.reload();
    }, 2000);
});

function initializeChart() {
    // 初始化空图表
    drawQuestionTypeChart(0, 0);
}

// 防抖函数，避免频繁重绘
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 窗口大小变化时重绘图表
const debouncedResize = debounce(() => {
    if (currentJsonData) {
        // 重新计算题目数量并重绘图表
        const choiceQuestions = currentJsonData.filter(q => !q.is_subjective).length;
        const subjectiveQuestions = currentJsonData.filter(q => q.is_subjective).length;
        drawQuestionTypeChart(choiceQuestions, subjectiveQuestions);
    } else {
        initializeChart();
    }
}, 250);

// 页面加载时刷新处理文件列表和初始化图表
document.addEventListener('DOMContentLoaded', function() {
    refreshProcessedFiles();
    initializeChart();

    // 监听窗口大小变化
    window.addEventListener('resize', debouncedResize);
});
</script>
{% endblock %}
