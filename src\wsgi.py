#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WSGI入口文件 - 用于生产环境部署
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 设置生产环境
os.environ['FLASK_ENV'] = 'production'

# 导入应用
from src.app import app, socketio

# WSGI应用对象
application = app

if __name__ == "__main__":
    # 如果直接运行此文件，启动开发服务器
    socketio.run(app, host='0.0.0.0', port=5002, debug=False)
