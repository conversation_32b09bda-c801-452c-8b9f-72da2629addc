# Gunicorn配置文件
import os

# 服务器配置
bind = "0.0.0.0:5002"  # 绑定地址和端口
workers = 2  # 工作进程数量（建议CPU核心数 * 2）
worker_class = "eventlet"  # 使用eventlet支持WebSocket
worker_connections = 1000  # 每个worker的连接数

# 应用配置
wsgi_module = "src.wsgi:application"  # WSGI模块
pythonpath = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 项目根目录

# 日志配置
accesslog = "logs/access.log"  # 访问日志
errorlog = "logs/error.log"   # 错误日志
loglevel = "info"             # 日志级别
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# 进程配置
daemon = False  # 是否后台运行
pidfile = "logs/gunicorn.pid"  # PID文件
user = None     # 运行用户（Linux下可设置）
group = None    # 运行组（Linux下可设置）

# 性能配置
keepalive = 2           # Keep-Alive连接时间
max_requests = 1000     # 每个worker处理的最大请求数
max_requests_jitter = 100  # 随机抖动
preload_app = True      # 预加载应用
timeout = 30            # 超时时间

# 安全配置
limit_request_line = 4096      # 请求行最大长度
limit_request_fields = 100     # 请求头字段数量限制
limit_request_field_size = 8192  # 请求头字段大小限制
