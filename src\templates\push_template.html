<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日地理一题 - 第{{ id }}题</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .meta-info {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .meta-item:last-child {
            margin-bottom: 0;
        }
        
        .meta-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .meta-label {
            font-weight: 600;
            color: #495057;
            min-width: 80px;
        }
        
        .meta-value {
            color: #6c757d;
        }
        
        .content {
            padding: 25px;
        }
        
        .shared-materials {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 8px 8px 0;
        }
        
        .shared-materials h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
            font-size: 16px;
        }
        
        .question-section {
            margin-bottom: 25px;
        }
        
        .question-text {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .options {
            margin: 20px 0;
        }
        
        .option {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .option:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .option.selected {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        
        .option.correct {
            background: #e8f5e8;
            border-color: #4caf50;
        }
        
        .option.incorrect {
            background: #ffebee;
            border-color: #f44336;
        }
        
        .option-label {
            font-weight: 600;
            color: #495057;
            margin-right: 10px;
            min-width: 25px;
        }
        
        .option-content {
            flex: 1;
        }
        
        .option-image {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin-top: 5px;
        }
        
        .images {
            margin: 20px 0;
        }
        
        .image-container {
            text-align: center;
            margin-bottom: 15px;
        }
        
        .question-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .answer-section {
            background: #f1f8e9;
            border-left: 4px solid #4caf50;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
            display: none;
        }
        
        .answer-section.show {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .answer-section h3 {
            margin: 0 0 10px 0;
            color: #2e7d32;
            font-size: 16px;
        }
        
        .correct-answer {
            font-size: 18px;
            font-weight: 600;
            color: #2e7d32;
            margin-bottom: 15px;
        }
        
        .analysis {
            line-height: 1.8;
            color: #424242;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .result-message {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 600;
            display: none;
        }
        
        .result-message.correct {
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #4caf50;
        }
        
        .result-message.incorrect {
            background: #ffebee;
            color: #c62828;
            border: 2px solid #f44336;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 5px;
        }
        
        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 20px;
            }
            
            .content {
                padding: 20px;
            }
            
            .meta-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .meta-label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>📍 每日地理一题 - 第{{ id }}题</h1>
        </div>
        
        <!-- 元数据信息 -->
        <div class="meta-info">
            <div class="meta-item">
                <svg class="meta-icon" viewBox="0 0 24 24" fill="#6c757d">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <span class="meta-label">题目来源:</span>
                <span class="meta-value">【来源：{{ paper_info }}】</span>
            </div>
            
            {% if tags %}
            <div class="meta-item">
                <svg class="meta-icon" viewBox="0 0 24 24" fill="#6c757d">
                    <path d="M17.63 5.84C17.27 5.33 16.67 5 16 5L5 5.01C3.9 5.01 3 5.9 3 7v10c0 1.1.9 2 2 2h11c.67 0 1.27-.33 1.63-.84L22 12l-4.37-6.16z"/>
                </svg>
                <span class="meta-label">知识点标签:</span>
                <div class="tags">
                    {% for tag in tags %}
                    <span class="tag">{{ tag }}</span>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <div class="meta-item">
                <svg class="meta-icon" viewBox="0 0 24 24" fill="#6c757d">
                    <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H18V1h-2v1H8V1H6v1H4.5C3.67 2 3 2.67 3 3.5v15C3 19.33 3.67 20 4.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C21 2.67 20.33 2 19.5 2z"/>
                </svg>
                <span class="meta-label">推送日期:</span>
                <span class="meta-value">{{ current_date }}</span>
            </div>
        </div>
        
        <!-- 主要内容 -->
        <div class="content">
            <!-- 共享材料 -->
            {% if shared_materials %}
            <div class="shared-materials">
                <h3>📋 材料</h3>
                <div>{{ shared_materials | safe }}</div>
            </div>
            {% endif %}
            
            <!-- 题目图片 -->
            {% if image_paths %}
            <div class="images">
                {% for image_path in image_paths %}
                <div class="image-container">
                    <!-- 使用HTTPS图片URL -->
                    <img src="{{ image_path }}"
                         alt="题目图片" class="question-image"
                         onerror="this.style.display='none'; this.parentNode.innerHTML='<p style=\'color:#999;text-align:center;padding:20px;\'>图片加载失败</p>';">
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- 题目内容 -->
            <div class="question-section">
                <div class="question-text">
                    <strong>{{ question_text | safe }}</strong>
                </div>
                
                <!-- 选项 -->
                {% if options and not is_subjective %}
                <div class="options">
                    {% for option in options %}
                    <div class="option" onclick="selectOption('{{ option.key }}', this)" data-key="{{ option.key }}">
                        <span class="option-label">{{ option.key }}.</span>
                        <div class="option-content">
                            {% if option.image_url %}
                                <!-- 选项是图片，使用HTTPS URL -->
                                <img src="{{ option.image_url }}"
                                     alt="选项{{ option.key }}" class="option-image"
                                     onerror="this.alt='图片加载失败';">
                            {% elif option.image_path %}
                                <!-- 选项是图片路径 -->
                                <img src="{{ image_url_base }}{{ image_folder }}/{{ option.image_path }}"
                                     alt="选项{{ option.key }}" class="option-image"
                                     onerror="this.alt='图片加载失败';">
                            {% elif option.value and (option.value.startswith('http') or option.value.endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'))) %}
                                <!-- 选项是图片URL -->
                                <img src="{{ option.value }}" alt="选项{{ option.key }}" class="option-image"
                                     onerror="this.alt='图片加载失败';">
                            {% else %}
                                <!-- 选项是文字 -->
                                {{ option.value }}
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <button class="submit-btn" onclick="submitAnswer()" disabled>确认答案</button>
                <div class="result-message" id="resultMessage"></div>
                {% endif %}
            </div>
            
            <!-- 答案和解析 -->
            <div class="answer-section" id="answerSection">
                <h3>✅ 答案与解析</h3>
                {% if correct_answer %}
                <div class="correct-answer">
                    正确答案：{{ correct_answer }}
                </div>
                {% endif %}
                {% if analysis %}
                <div class="analysis">
                    <strong>解析：</strong>{{ analysis | safe }}
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- 页脚 -->
        <div class="footer">
            <p>🌍 每日地理一题，助力地理学习</p>
            <p>本题目来源于真实考试试卷，仅供学习交流使用</p>
        </div>
    </div>

    <script>
        let selectedOption = null;
        const correctAnswer = '{{ correct_answer }}';
        
        function selectOption(key, element) {
            // 移除之前选中的选项
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // 选中当前选项
            element.classList.add('selected');
            selectedOption = key;
            
            // 启用提交按钮
            document.querySelector('.submit-btn').disabled = false;
        }
        
        function submitAnswer() {
            if (!selectedOption) return;
            
            const resultMessage = document.getElementById('resultMessage');
            const answerSection = document.getElementById('answerSection');
            const submitBtn = document.querySelector('.submit-btn');
            
            // 禁用提交按钮
            submitBtn.disabled = true;
            submitBtn.textContent = '已提交';
            
            // 显示结果
            const isCorrect = selectedOption === correctAnswer;
            
            if (isCorrect) {
                resultMessage.textContent = '🎉 恭喜你，答对了！';
                resultMessage.className = 'result-message correct';
            } else {
                resultMessage.textContent = '❌ 答案不正确，再接再厉！';
                resultMessage.className = 'result-message incorrect';
            }
            
            resultMessage.style.display = 'block';
            
            // 标记选项的对错
            document.querySelectorAll('.option').forEach(opt => {
                const key = opt.dataset.key;
                if (key === correctAnswer) {
                    opt.classList.add('correct');
                } else if (key === selectedOption && key !== correctAnswer) {
                    opt.classList.add('incorrect');
                }
                // 禁用点击
                opt.onclick = null;
                opt.style.cursor = 'default';
            });
            
            // 显示答案解析
            answerSection.classList.add('show');
        }
        
        // 如果是主观题，直接显示答案
        {% if is_subjective %}
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('answerSection').classList.add('show');
        });
        {% endif %}
    </script>
</body>
</html>
