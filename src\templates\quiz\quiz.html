<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线答题 - 地理题库系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .quiz-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .question-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .timer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: bold;
            display: inline-block;
        }
        .option-btn {
            background: rgba(102, 126, 234, 0.1);
            border: 2px solid rgba(102, 126, 234, 0.3);
            color: #667eea;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-align: left;
            width: 100%;
        }
        .option-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }
        .option-btn.selected {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        .option-btn.correct {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }
        .option-btn.incorrect {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .result-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 1rem;
            display: none;
        }
        .result-correct {
            border-left: 5px solid #28a745;
        }
        .result-incorrect {
            border-left: 5px solid #dc3545;
        }
        .favorite-btn {
            background: none;
            border: 2px solid #ffc107;
            color: #ffc107;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        .favorite-btn.favorited {
            background: #ffc107;
            color: white;
        }
        .favorite-btn:hover {
            background: #ffc107;
            color: white;
        }
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        .option-image {
            max-width: 100%;
            max-height: 150px;
            border-radius: 8px;
            margin: 5px 0;
            display: block;
        }
        .option-btn {
            min-height: auto;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .option-btn img {
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('quiz.home') }}">
                <i class="bi bi-geo-alt-fill text-primary me-2"></i>
                地理题库系统
            </a>
            
            <div class="d-flex align-items-center">
                <div class="timer me-3" id="timer">
                    <i class="bi bi-clock me-1"></i>
                    <span id="timeDisplay">00:00</span>
                </div>
                <a href="{{ url_for('quiz.home') }}" class="btn btn-outline-primary">
                    <i class="bi bi-house me-1"></i>返回首页
                </a>
            </div>
        </div>
    </nav>

    <!-- 答题区域 -->
    <div class="quiz-container">
        <!-- 加载状态 -->
        <div id="loadingState" class="question-card">
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
        </div>

        <!-- 题目卡片 -->
        <div id="questionCard" class="question-card" style="display: none;">
            <div class="d-flex justify-content-between align-items-start mb-4">
                <div class="flex-grow-1">
                    <div class="d-flex flex-wrap gap-2 mb-3" id="questionTags"></div>
                    <h4 id="questionText" class="mb-3"></h4>
                </div>
                <button class="favorite-btn" id="favoriteBtn" onclick="toggleFavorite()">
                    <i class="bi bi-heart"></i>
                </button>
            </div>
            
            <!-- 题目图片 -->
            <div id="questionImages" class="mb-4"></div>
            
            <!-- 选项 -->
            <div id="optionsContainer" class="mb-4"></div>
            
            <!-- 操作按钮 -->
            <div class="d-flex justify-content-between">
                <button class="btn btn-secondary" onclick="loadNewQuestion()">
                    <i class="bi bi-skip-forward me-2"></i>跳过此题
                </button>
                <button class="btn btn-primary" id="submitBtn" onclick="submitAnswer()">
                    <i class="bi bi-check-circle me-2"></i>提交答案
                </button>
            </div>
        </div>

        <!-- 答题结果 -->
        <div id="resultCard" class="result-card">
            <div class="d-flex align-items-center mb-3">
                <div class="me-3">
                    <i id="resultIcon" class="bi bi-check-circle-fill text-success" style="font-size: 2rem;"></i>
                </div>
                <div>
                    <h5 id="resultTitle" class="mb-1">回答正确！</h5>
                    <p id="resultText" class="mb-0 text-muted">恭喜您答对了这道题</p>
                </div>
            </div>
            
            <div class="mb-3">
                <strong>正确答案：</strong>
                <span id="correctAnswer" class="text-success"></span>
            </div>
            
            <div class="mb-4" id="analysisSection">
                <strong>题目解析：</strong>
                <div id="analysisText" class="mt-2 p-3 bg-light rounded"></div>
            </div>
            
            <div class="d-flex justify-content-center gap-3">
                <button class="btn btn-primary" onclick="loadNewQuestion()">
                    <i class="bi bi-arrow-right me-2"></i>下一题
                </button>
                <button class="btn btn-outline-primary" onclick="backToHome()">
                    <i class="bi bi-house me-2"></i>返回首页
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentQuestion = null;
        let selectedAnswer = null;
        let startTime = null;
        let timerInterval = null;
        let isAnswered = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const mode = urlParams.get('mode') || 'random';
            const tag = urlParams.get('tag') || '';
            
            loadQuestion(mode, tag);
        });

        // 加载题目
        function loadQuestion(mode = 'random', tag = '') {
            showLoading();
            
            let url = '/quiz/api/question?mode=' + mode;
            if (tag) {
                url += '&tag=' + encodeURIComponent(tag);
            }
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentQuestion = data.question;
                        displayQuestion();
                        startTimer();
                    } else {
                        showError(data.message || '加载题目失败');
                    }
                })
                .catch(error => {
                    console.error('加载题目失败:', error);
                    showError('网络错误，请稍后重试');
                });
        }

        // 显示题目
        function displayQuestion() {
            if (!currentQuestion) return;

            // 显示题目文本
            document.getElementById('questionText').innerHTML = currentQuestion.question_text;
            
            // 显示标签
            const tagsContainer = document.getElementById('questionTags');
            if (currentQuestion.tags && currentQuestion.tags.length > 0) {
                tagsContainer.innerHTML = currentQuestion.tags.map(tag => 
                    `<span class="badge bg-secondary">${tag}</span>`
                ).join('');
            } else {
                tagsContainer.innerHTML = '';
            }
            
            // 显示图片
            const imagesContainer = document.getElementById('questionImages');
            if (currentQuestion.image_paths && currentQuestion.image_paths.length > 0) {
                imagesContainer.innerHTML = currentQuestion.image_paths.map(path => 
                    `<img src="${path}" class="img-fluid mb-2" style="max-height: 300px;" alt="题目图片">`
                ).join('');
            } else {
                imagesContainer.innerHTML = '';
            }
            
            // 显示选项
            const optionsContainer = document.getElementById('optionsContainer');
            if (currentQuestion.options && currentQuestion.options.length > 0) {
                optionsContainer.innerHTML = currentQuestion.options.map((option, index) => {
                    // 处理选项数据格式
                    let optionKey, optionContent;

                    if (typeof option === 'object' && option !== null) {
                        // 选项是对象格式 {key: "A", value: "选项内容", image_path: null}
                        optionKey = option.key || String.fromCharCode(65 + index);

                        if (option.image_url) {
                            // 选项是图片URL
                            optionContent = `<img src="${option.image_url}" alt="选项${optionKey}" class="option-image" style="max-width: 200px; max-height: 150px;" onerror="this.alt='图片加载失败';">`;
                        } else if (option.image_path) {
                            // 选项是图片路径
                            optionContent = `<img src="/static/images/${option.image_path}" alt="选项${optionKey}" class="option-image" style="max-width: 200px; max-height: 150px;" onerror="this.alt='图片加载失败';">`;
                        } else if (option.value && (option.value.startsWith('http') || option.value.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i))) {
                            // 选项值是图片URL
                            optionContent = `<img src="${option.value}" alt="选项${optionKey}" class="option-image" style="max-width: 200px; max-height: 150px;" onerror="this.alt='图片加载失败';">`;
                        } else {
                            // 选项是文字
                            optionContent = option.value || option.text || option.content || '';
                        }
                    } else {
                        // 选项是字符串格式
                        optionKey = String.fromCharCode(65 + index);
                        optionContent = option;
                    }

                    return `
                        <button class="option-btn" onclick="selectOption('${optionKey}', this)">
                            <strong>${optionKey}.</strong> ${optionContent}
                        </button>
                    `;
                }).join('');
            } else {
                optionsContainer.innerHTML = '<p class="text-muted">此题为主观题，暂不支持在线答题</p>';
            }
            
            // 设置收藏状态
            const favoriteBtn = document.getElementById('favoriteBtn');
            if (currentQuestion.is_favorited) {
                favoriteBtn.classList.add('favorited');
                favoriteBtn.innerHTML = '<i class="bi bi-heart-fill"></i>';
            } else {
                favoriteBtn.classList.remove('favorited');
                favoriteBtn.innerHTML = '<i class="bi bi-heart"></i>';
            }
            
            // 重置状态
            selectedAnswer = null;
            isAnswered = false;
            document.getElementById('submitBtn').disabled = false;
            document.getElementById('resultCard').style.display = 'none';
            
            hideLoading();
        }

        // 选择选项
        function selectOption(answer, element) {
            if (isAnswered) return;
            
            // 清除之前的选择
            document.querySelectorAll('.option-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            // 选中当前选项
            element.classList.add('selected');
            selectedAnswer = answer;
            
            // 启用提交按钮
            document.getElementById('submitBtn').disabled = false;
        }

        // 提交答案
        function submitAnswer() {
            if (!selectedAnswer || isAnswered) return;
            
            const timeSpent = Math.floor((Date.now() - startTime) / 1000);
            
            fetch('/quiz/api/answer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question_id: currentQuestion.id,
                    user_answer: selectedAnswer,
                    time_spent: timeSpent
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult(data);
                    isAnswered = true;
                    stopTimer();
                } else {
                    alert(data.message || '提交失败');
                }
            })
            .catch(error => {
                console.error('提交答案失败:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 显示答题结果
        function showResult(result) {
            const resultCard = document.getElementById('resultCard');
            const resultIcon = document.getElementById('resultIcon');
            const resultTitle = document.getElementById('resultTitle');
            const resultText = document.getElementById('resultText');
            const correctAnswer = document.getElementById('correctAnswer');
            const analysisText = document.getElementById('analysisText');
            
            // 更新选项样式
            document.querySelectorAll('.option-btn').forEach(btn => {
                btn.disabled = true;
                const btnText = btn.textContent.trim();
                const btnLetter = btnText.charAt(0);
                
                if (btnLetter === result.correct_answer) {
                    btn.classList.add('correct');
                } else if (btnLetter === selectedAnswer && !result.is_correct) {
                    btn.classList.add('incorrect');
                }
            });
            
            // 设置结果样式
            if (result.is_correct) {
                resultCard.className = 'result-card result-correct';
                resultIcon.className = 'bi bi-check-circle-fill text-success';
                resultTitle.textContent = '回答正确！';
                resultText.textContent = '恭喜您答对了这道题';
            } else {
                resultCard.className = 'result-card result-incorrect';
                resultIcon.className = 'bi bi-x-circle-fill text-danger';
                resultTitle.textContent = '回答错误';
                resultText.textContent = '继续努力，下次一定能答对';
            }
            
            correctAnswer.textContent = result.correct_answer;
            analysisText.innerHTML = result.analysis || '暂无解析';
            
            resultCard.style.display = 'block';
        }

        // 切换收藏状态
        function toggleFavorite() {
            if (!currentQuestion) return;
            
            fetch('/quiz/api/favorite', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question_id: currentQuestion.id
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const favoriteBtn = document.getElementById('favoriteBtn');
                    if (data.action === 'added') {
                        favoriteBtn.classList.add('favorited');
                        favoriteBtn.innerHTML = '<i class="bi bi-heart-fill"></i>';
                        currentQuestion.is_favorited = true;
                    } else {
                        favoriteBtn.classList.remove('favorited');
                        favoriteBtn.innerHTML = '<i class="bi bi-heart"></i>';
                        currentQuestion.is_favorited = false;
                    }
                } else {
                    alert(data.message || '操作失败');
                }
            })
            .catch(error => {
                console.error('切换收藏状态失败:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 加载新题目
        function loadNewQuestion() {
            const urlParams = new URLSearchParams(window.location.search);
            const mode = urlParams.get('mode') || 'random';
            const tag = urlParams.get('tag') || '';
            
            loadQuestion(mode, tag);
        }

        // 返回首页
        function backToHome() {
            window.location.href = '/quiz/home';
        }

        // 计时器相关
        function startTimer() {
            startTime = Date.now();
            timerInterval = setInterval(updateTimer, 1000);
        }

        function stopTimer() {
            if (timerInterval) {
                clearInterval(timerInterval);
                timerInterval = null;
            }
        }

        function updateTimer() {
            if (!startTime) return;
            
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            
            document.getElementById('timeDisplay').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // 显示/隐藏加载状态
        function showLoading() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('questionCard').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('questionCard').style.display = 'block';
        }

        function showError(message) {
            document.getElementById('loadingState').innerHTML = `
                <div class="text-center text-danger">
                    <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">${message}</h5>
                    <button class="btn btn-primary mt-3" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise me-2"></i>重新加载
                    </button>
                </div>
            `;
        }

        // 页面卸载时清理计时器
        window.addEventListener('beforeunload', function() {
            stopTimer();
        });
    </script>
</body>
</html>
