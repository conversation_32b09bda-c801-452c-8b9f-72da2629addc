<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 地理题库系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .main-content {
            padding: 2rem 0;
        }
        .profile-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .stat-item {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #667eea;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 25px;
        }
        .history-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        .history-item.correct {
            border-left-color: #28a745;
        }
        .history-item.incorrect {
            border-left-color: #dc3545;
        }
        .mistake-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #dc3545;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 8px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .pagination .page-link {
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }
        .pagination .page-item.active .page-link {
            background: #667eea;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('quiz.home') }}">
                <i class="bi bi-geo-alt-fill text-primary me-2"></i>
                地理题库系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('quiz.home') }}">
                            <i class="bi bi-house me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('quiz.quiz_page') }}">
                            <i class="bi bi-pencil-square me-1"></i>开始刷题
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('quiz.profile') }}">
                            <i class="bi bi-person me-1"></i>个人中心
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('quiz.profile') }}">
                                <i class="bi bi-person me-2"></i>个人中心
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container main-content">
        <!-- 用户信息卡片 -->
        <div class="profile-card">
            <div class="row align-items-center mb-4">
                <div class="col-auto">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                         style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="bi bi-person-fill"></i>
                    </div>
                </div>
                <div class="col">
                    <h3 class="mb-1">{{ current_user.username }}</h3>
                    <p class="text-muted mb-0">{{ current_user.email }}</p>
                    <small class="text-muted">
                        注册时间：{{ current_user.created_at.strftime('%Y-%m-%d') if current_user.created_at else '未知' }}
                    </small>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('quiz.quiz_page') }}" class="btn btn-primary">
                        <i class="bi bi-play-fill me-2"></i>开始刷题
                    </a>
                </div>
            </div>

            <!-- 统计数据 -->
            <div class="stats-grid" id="statsGrid">
                <div class="stat-item">
                    <div class="stat-number" id="totalCount">-</div>
                    <div class="stat-label">总答题数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number text-success" id="correctCount">-</div>
                    <div class="stat-label">正确题数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number text-info" id="accuracyRate">-</div>
                    <div class="stat-label">正确率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number text-warning" id="favoriteCount">-</div>
                    <div class="stat-label">收藏题数</div>
                </div>
            </div>
        </div>

        <!-- 选项卡内容 -->
        <div class="profile-card">
            <ul class="nav nav-tabs mb-4" id="profileTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" 
                            type="button" role="tab">
                        <i class="bi bi-clock-history me-2"></i>答题历史
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="mistakes-tab" data-bs-toggle="tab" data-bs-target="#mistakes" 
                            type="button" role="tab">
                        <i class="bi bi-x-circle me-2"></i>错题本
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="profileTabContent">
                <!-- 答题历史 -->
                <div class="tab-pane fade show active" id="history" role="tabpanel">
                    <div id="historyContent">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                    <nav id="historyPagination" class="mt-4"></nav>
                </div>

                <!-- 错题本 -->
                <div class="tab-pane fade" id="mistakes" role="tabpanel">
                    <div id="mistakesContent">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                    <nav id="mistakesPagination" class="mt-4"></nav>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentHistoryPage = 1;
        let currentMistakesPage = 1;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadHistory(1);
            
            // 监听选项卡切换
            document.getElementById('mistakes-tab').addEventListener('shown.bs.tab', function() {
                if (currentMistakesPage === 1) {
                    loadMistakes(1);
                }
            });
        });

        // 加载统计数据
        function loadStats() {
            fetch('/quiz/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        document.getElementById('totalCount').textContent = stats.total_count;
                        document.getElementById('correctCount').textContent = stats.correct_count;
                        document.getElementById('accuracyRate').textContent = stats.accuracy_rate + '%';
                        document.getElementById('favoriteCount').textContent = stats.favorite_count;
                    }
                })
                .catch(error => {
                    console.error('加载统计数据失败:', error);
                });
        }

        // 加载答题历史
        function loadHistory(page) {
            currentHistoryPage = page;
            
            fetch(`/quiz/api/history?page=${page}&per_page=10`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayHistory(data.history);
                        updateHistoryPagination(page, data.history.length);
                    } else {
                        document.getElementById('historyContent').innerHTML = `
                            <div class="text-center text-muted">
                                <i class="bi bi-info-circle me-2"></i>
                                暂无答题历史
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载答题历史失败:', error);
                    document.getElementById('historyContent').innerHTML = `
                        <div class="text-center text-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            加载失败
                        </div>
                    `;
                });
        }

        // 显示答题历史
        function displayHistory(history) {
            const container = document.getElementById('historyContent');
            
            if (history.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="bi bi-info-circle me-2"></i>
                        暂无答题历史
                    </div>
                `;
                return;
            }

            container.innerHTML = history.map(item => {
                const isCorrect = item.is_correct;
                const statusClass = isCorrect ? 'correct' : 'incorrect';
                const statusIcon = isCorrect ? 'bi-check-circle-fill text-success' : 'bi-x-circle-fill text-danger';
                const statusText = isCorrect ? '正确' : '错误';
                
                return `
                    <div class="history-item ${statusClass}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi ${statusIcon} me-2"></i>
                                    <strong>${statusText}</strong>
                                    <span class="ms-auto text-muted small">
                                        ${new Date(item.answered_at).toLocaleString()}
                                    </span>
                                </div>
                                <p class="mb-2">${item.question_text}</p>
                                <div class="small text-muted">
                                    <span class="me-3">您的答案: <strong>${item.user_answer}</strong></span>
                                    <span>用时: ${item.time_spent}秒</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 更新历史记录分页
        function updateHistoryPagination(currentPage, itemCount) {
            const pagination = document.getElementById('historyPagination');
            
            if (itemCount < 10 && currentPage === 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHtml = '<ul class="pagination justify-content-center">';
            
            if (currentPage > 1) {
                paginationHtml += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadHistory(${currentPage - 1})">上一页</a>
                    </li>
                `;
            }
            
            paginationHtml += `
                <li class="page-item active">
                    <span class="page-link">${currentPage}</span>
                </li>
            `;
            
            if (itemCount === 10) {
                paginationHtml += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadHistory(${currentPage + 1})">下一页</a>
                    </li>
                `;
            }
            
            paginationHtml += '</ul>';
            pagination.innerHTML = paginationHtml;
        }

        // 加载错题本
        function loadMistakes(page) {
            currentMistakesPage = page;
            
            fetch(`/quiz/api/mistakes?page=${page}&per_page=10`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayMistakes(data.mistakes);
                        updateMistakesPagination(page, data.mistakes.length);
                    } else {
                        document.getElementById('mistakesContent').innerHTML = `
                            <div class="text-center text-muted">
                                <i class="bi bi-info-circle me-2"></i>
                                暂无错题记录
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载错题本失败:', error);
                    document.getElementById('mistakesContent').innerHTML = `
                        <div class="text-center text-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            加载失败
                        </div>
                    `;
                });
        }

        // 显示错题本
        function displayMistakes(mistakes) {
            const container = document.getElementById('mistakesContent');
            
            if (mistakes.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="bi bi-emoji-smile me-2"></i>
                        太棒了！暂无错题记录
                    </div>
                `;
                return;
            }

            container.innerHTML = mistakes.map(item => {
                const optionsHtml = item.options.map((option, index) => {
                    // 处理选项数据格式
                    let optionKey, optionContent;

                    if (typeof option === 'object' && option !== null) {
                        // 选项是对象格式
                        optionKey = option.key || String.fromCharCode(65 + index);

                        if (option.image_url) {
                            optionContent = `<img src="${option.image_url}" alt="选项${optionKey}" style="max-width: 150px; max-height: 100px; border-radius: 5px;" onerror="this.alt='图片加载失败';">`;
                        } else if (option.image_path) {
                            optionContent = `<img src="/static/images/${option.image_path}" alt="选项${optionKey}" style="max-width: 150px; max-height: 100px; border-radius: 5px;" onerror="this.alt='图片加载失败';">`;
                        } else if (option.value && (option.value.startsWith('http') || option.value.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i))) {
                            optionContent = `<img src="${option.value}" alt="选项${optionKey}" style="max-width: 150px; max-height: 100px; border-radius: 5px;" onerror="this.alt='图片加载失败';">`;
                        } else {
                            optionContent = option.value || option.text || option.content || '';
                        }
                    } else {
                        // 选项是字符串格式
                        optionKey = String.fromCharCode(65 + index);
                        optionContent = option;
                    }

                    const isCorrect = optionKey === item.correct_answer;
                    const optionClass = isCorrect ? 'text-success fw-bold' : '';
                    return `<div class="${optionClass}">${optionKey}. ${optionContent}</div>`;
                }).join('');

                const tagsHtml = item.tags.map(tag => 
                    `<span class="badge bg-secondary me-1">${tag}</span>`
                ).join('');

                return `
                    <div class="mistake-item">
                        <div class="mb-2">
                            ${tagsHtml}
                        </div>
                        <h6 class="mb-3">${item.question_text}</h6>
                        <div class="mb-3">
                            ${optionsHtml}
                        </div>
                        <div class="mb-2">
                            <strong class="text-success">正确答案: ${item.correct_answer}</strong>
                        </div>
                        ${item.analysis ? `
                            <div class="small text-muted">
                                <strong>解析:</strong> ${item.analysis}
                            </div>
                        ` : ''}
                        <div class="text-end mt-2">
                            <small class="text-muted">
                                错误时间: ${new Date(item.answered_at).toLocaleString()}
                            </small>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 更新错题本分页
        function updateMistakesPagination(currentPage, itemCount) {
            const pagination = document.getElementById('mistakesPagination');
            
            if (itemCount < 10 && currentPage === 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHtml = '<ul class="pagination justify-content-center">';
            
            if (currentPage > 1) {
                paginationHtml += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadMistakes(${currentPage - 1})">上一页</a>
                    </li>
                `;
            }
            
            paginationHtml += `
                <li class="page-item active">
                    <span class="page-link">${currentPage}</span>
                </li>
            `;
            
            if (itemCount === 10) {
                paginationHtml += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadMistakes(${currentPage + 1})">下一页</a>
                    </li>
                `;
            }
            
            paginationHtml += '</ul>';
            pagination.innerHTML = paginationHtml;
        }
    </script>
</body>
</html>
