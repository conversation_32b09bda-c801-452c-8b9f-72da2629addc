"""
AI提示词管理模块
包含所有预处理相关的AI提示词，不依赖外部文件
"""

# 统一的AI处理提示词
AI_PROMPT_UNIFIED = """你是一个顶级的、遵循严格指令的教育资料处理专家。你的唯一任务是将用户发来的任何试卷文本（无论是选择题还是非选择题），都严格地转换为以下定义的"格式化文本"。

【最高优先级指令】
1. 绝对禁止任何形式的额外评论或解释。你的输出必须是纯粹的、可以直接被程序解析的格式化文本。
2. 每一道"小题"都必须被一个独立的 `<QUESTION_START>` 和 `<QUESTION_END>` 块包裹。
3. 必须严格使用提供的所有标签，不得增删或修改标签名。

【统一输出格式】
<QUESTION_START>
<ID>题号</ID>
<GROUP_ID>题组号</GROUP_ID>
<ORDER_IN_GROUP>在题组内的顺序</ORDER_IN_GROUP>
<IS_GROUP_HEADER>是否为题组头</IS_GROUP_HEADER>
<IS_SUBJECTIVE>是否为主观题</IS_SUBJECTIVE>
<SHARED_MATERIALS>共享的文字材料</SHARED_MATERIALS>
<IMAGE_PATHS>共享或独有的图片路径列表</IMAGE_PATHS>
<TEXT>本小题的具体题干</TEXT>
<OPTIONS>
A. 选项A内容
B. 选项B内容
C. 选项C内容
D. 选项D内容
</OPTIONS>
<ANSWER>本小题的答案</ANSWER>
<ANALYSIS>本小题的解析</ANALYSIS>
<TAGS>知识点标签列表</TAGS>
</QUESTION_END>

【核心处理规则】
1. ID: 填写每个小题的题号数字。对于非选择题的多个小问（如26题的(1)(2)(3)问），它们的ID都填写大题号26。
2. GROUP_ID: 用于标识"题组"的ID。如果多道题共享一份材料，它们的GROUP_ID必须相同，且等于题组第一题的ID。对于非选择题，其所有小问的GROUP_ID都等于大题号。对于独立的单题，其GROUP_ID等于它自己的ID。
3. ORDER_IN_GROUP: 在题组内的顺序。对于独立题目，此值为1。对于题组，按小题顺序编号，如1, 2, 3...。
4. IS_GROUP_HEADER: 是否为题组的"头"，布尔值（true/false）。在同一个GROUP_ID的题组中，有且仅有ORDER_IN_GROUP为1的题目，此值为true。所有其他独立题目或非头部的题组题目，此值均为false。
5. IS_SUBJECTIVE: 是否为主观题，布尔值（true/false）。选择题为false。填空、简答等非选择题为true。
6. SHARED_MATERIALS: 共享的文字材料。此内容只应出现在IS_GROUP_HEADER为true的块中，其他块中此标签内容为空。
7. IMAGE_PATHS: 图片路径列表，格式为["路径1", "路径2"]。共享图片或单题独有图片放在这里。没有则为空列表[]。注意：如果图片属于选项，则此字段应为空，图片路径应在<OPTIONS>中特殊标注。
8. OPTIONS: 选项内容。此标签块仅在IS_SUBJECTIVE为false的选择题中出现。如果选项本身是图片，则选项内容应写成 `(图: option_题号_选项字母.png)` 的格式。
9. ANSWER: 答案。选择题为单个大写字母。非选择题为完整的参考答案文本。
10. TAGS: 知识点标签。提取2-3个核心知识点，用英文逗号分隔。

请处理以下内容：

[在这里粘贴需要处理的试题文本]"""

# 文档分析提示词
DOCUMENT_ANALYSIS_PROMPT = """作为试卷分析专家，请仔细分析我上传的试卷文档内容的结构，提取关键信息：
1. 识别所有题目编号和题型（仅有"选择题"和"非选择题"两种类型）
2. 识别题目的分组情况
   - 对于非选择题：如26题及其子题(26(1)、26(2)、26(3))应该属于同一个题组
   - 如果题目之间共享背景材料，应归为同一题组
3. 统计各类题型的数量
4. 识别每个题组的起始和结束题号
5. 识别每个题组的共享材料内容（如有）
6. 请分析图片与题目的关系（一般材料下方的是共享图片，题目下方的是题目图片，选项图片仅在选项(A.B.C.D.)后边，请仔细识别）
7. wmf格式的图片请直接忽略，不要放入图片信息中，图片命名为image1.png或者image2.jpeg等，不需要media等其他文字，序号为图片的真实顺序（wmf格式也算序号）

请以JSON格式返回分析结果，你必须严格按照以下格式输出JSON：
{
    "题型统计": {
        "选择题": 数量,
        "非选择题": 数量
    },
    "题组信息": [
        {
            "组号": 1,
            "起始题号": "1",
            "结束题号": "2", 
            "题型": "选择题"或"非选择题",
            "共享材料": "材料内容（如有）",
            "图片信息": {
                "共享图片": [图片列表],
                "题目图片": {
                    "题号": [图片列表]
                },
                "选项图片": {
                    "题号_选项": [图片列表]
                }
            }
        }
        ...
    ],
    "总题目数": 数量,
    "图片分析": {
        "共享材料图片": [图片列表],
        "题目图片": [图片列表],
        "选项图片": [图片列表],
        "未分类图片": [图片列表]
    }
}"""


def get_ai_prompt_unified(content: str = "") -> str:
    """获取统一的AI处理提示词"""
    return AI_PROMPT_UNIFIED.format(content=content)


def get_document_analysis_prompt() -> str:
    """获取文档分析提示词"""
    return DOCUMENT_ANALYSIS_PROMPT


def get_processing_context_info(question_info: dict, current_group_index: int, total_groups: int, image_info_str: str = "") -> str:
    """生成处理上下文信息"""
    question_type = question_info.get('题型', '未知')
    shared_materials = question_info.get('共享材料', '')
    
    context_info = f"""
【处理上下文】
当前处理的是第{current_group_index+1}组（共{total_groups}组）
题目类型：{question_type}
题组号：{question_info.get('组号', '')}
起始题号：{question_info.get('起始题号', '')}
结束题号：{question_info.get('结束题号', '')}
共享材料：{shared_materials}
图片信息参考:
{image_info_str if image_info_str else "本题组无图片信息。"}


【重要提示】
1. 对于选择题，必须在每个题目中包含SHARED_MATERIALS标签
2. 对于非选择题，只在大题的第一个块中包含SHARED_MATERIALS标签，小题不需要包含
3. 如果题目或选项中有图片，请加上对应的图片说明标签，例如<IMAGE>图1</IMAGE>
"""
    return context_info
